import { configureStore } from '@reduxjs/toolkit'
import counterReducer from '@/features/counter/index.store'
import popupReducer from '@/features/popup/index.store'

export const store = configureStore({
  reducer: {
    counter: counterReducer,
    popup: popupReducer,
  },
})

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>
// Inferred type: {posts: PostsState, comments: CommentsState, users: UsersState}
export type AppDispatch = typeof store.dispatch
