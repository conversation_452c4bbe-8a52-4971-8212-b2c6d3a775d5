import { createContext, useContext, useMemo } from "react";
import {
  createStore as createZustandStore,
  StateCreator,
  StoreApi,
  useStore,
} from "zustand";
import { subscribeWithSelector } from "zustand/middleware";

export interface DolphinOption {
  debug?: boolean;
}

export interface AutoFetchState<Data = any> {
  unload: boolean;
  default: Data;
  service: (params?: Record<string, any>) => Promise<Data>;
  params?: Record<string, any>;
  loading?: boolean;
}

type Write<T, U> = Omit<T, keyof U> & U;

type StoreSubscribeWithSelector<T> = {
  subscribe: {
    (
      listener: (selectedState: T, previousSelectedState: T) => void
    ): () => void;
    <U>(
      selector: (state: T) => U,
      listener: (selectedState: U, previousSelectedState: U) => void,
      options?: {
        equalityFn?: (a: U, b: U) => boolean;
        fireImmediately?: boolean;
      }
    ): () => void;
  };
};
interface ZustandStore<T> {
  Provider: ({
    value,
    children,
  }: {
    value: StateCreator<T>;
    children: React.ReactNode;
  }) => JSX.Element;
  useStore: <U>(selector: (state: T) => U) => U;
  useSubscribe: () => StoreSubscribeWithSelector<T>["subscribe"];
  useGet: () => StoreApi<T>["getState"];
  useSet: () => StoreApi<T>["setState"];
}

export const createZustandContext = <
  T extends Record<string, any>,
>(): ZustandStore<T> => {
  // @ts-ignore
  const Context = createContext<
    Write<StoreApi<T>, StoreSubscribeWithSelector<T>>
  >({});

  const Provider: ZustandStore<T>["Provider"] = ({ value, children }) => {
    const store = useMemo(
      () => createZustandStore<T>(subscribeWithSelector(() => value)),
      []
    );

    return <Context.Provider value={store}>{children}</Context.Provider>;
  };

  return {
    Provider,
    useStore: (selector) => {
      const store = useContext(Context);
      if (!store.getState) return;
      return useStore(store, selector);
    },
    useSubscribe: () => {
      const store = useContext(Context);
      if (!store.getState) return;
      return store.subscribe;
    },
    useGet: () => {
      const store = useContext(Context);
      if (!store.getState) return;
      return store.getState;
    },
    useSet: () => {
      const store = useContext(Context);
      if (!store.getState) return;
      return store.setState;
    },
  };
};
