import { Button } from '@tita/ui';
import { createZustandContext } from '&/libs/zustand';
import React, { FC, useMemo } from 'react';
import styled from 'styled-components';

export const {
  Provider: DemoInnerProvider,
  useStore: useDemoStore,
  useSubscribe: useDemoSubscribe,
  useSet: useDemoSet,
  useGet: useDemoGet,
} = createZustandContext<{
  count: number;
}>();

export interface IZustandDemoProps {
  children?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}

const CountPreview = () => {
  const count = useDemoStore((state) => state.count);
  return <div>Count: {count}</div>;
};

const ZustandDemoInner: FC<IZustandDemoProps> = React.memo(({ className, style }) => {
  console.log('render ZustandDemoInner');

  const set = useDemoSet();
  return (
    <ZustandDemoStyle className={className} style={style}>
      <Button
        onClick={() => {
          set((state) => ({ count: state.count + 1 }));
        }}
      >
        add
      </Button>
      <CountPreview />
    </ZustandDemoStyle>
  );
});

export const ZustandDemo: FC<IZustandDemoProps> = React.memo((props) => {
  const context = useMemo(
    () => ({
      count: 0,
    }),
    [],
  );

  return (
    <DemoInnerProvider value={context}>
      <ZustandDemoInner {...props} />
    </DemoInnerProvider>
  );
});

const ZustandDemoStyle = styled.div``;

export default ZustandDemo;
