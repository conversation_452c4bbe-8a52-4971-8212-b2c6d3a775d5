import { useEffect } from 'react'
import { Observer } from './observer'

export type DragInfo = {
  type: string,
  item: any,
  onDragEnd: (result: any) => void
}

export type DrapInfo = {
  /**
   * 允许监听哪些类型的 drag 事件
   */
  accept: string[],
  item: any,
  onDragOver: (item: any, info: DragInfo) => any
  onDragExit: (item: any, info: DragInfo) => any
  onDragOverEnd: (item: any, info: DragInfo) => any
}

const observer = new Observer<{
  onDraging: DragInfo
}>()

export const useDrag = () => {
  const onDragStart = (info: DragInfo) => {
    observer.emit('onDraging', info)
  }
  return { onDragStart }
}

export const useDrap = ({ accept }: DrapInfo) => {
  useEffect(() => {
    const onDraging = (dragInfo: DragInfo) => {
      if (!accept.includes(dragInfo.type)) return
    }
    observer.on('onDraging', onDraging)

    return () => {
      observer.off('onDraging', onDraging)
    }
  }, [accept])
}