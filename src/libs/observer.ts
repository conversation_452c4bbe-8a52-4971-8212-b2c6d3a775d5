export class Observer<Options extends Record<string, any>> {
  // @ts-ignore
  events: {
    [K in keyof Options]: ((eventData: Options[K]) => void)[]
  } = {}
  // 实现订阅
  on<K extends keyof Options>(
    type: K,
    callBack: (eventData: Options[K]) => void
  ) {
    if (!this.events[type]) {
      this.events[type] = [callBack]
    } else {
      this.events[type].push(callBack)
    }
  }
  // 删除订阅
  off<K extends keyof Options>(
    type: K,
    callBack: (eventData: Options[K]) => void
  ) {
    if (!this.events[type]) return
    this.events[type] = this.events[type].filter((item) => {
      return item !== callBack
    })
  }
  // 只执行一次订阅事件
  once<K extends keyof Options>(
    type: K,
    callBack: (eventData: Options[K]) => void
  ) {
    const _ = this
    function fn(eventData: Options[K]) {
      callBack(eventData)
      _.off(type, fn)
    }
    this.on(type, fn)
  }
  // 触发事件
  emit<K extends keyof Options>(type: K, eventData: Options[K]) {
    this.events[type] &&
      this.events[type].forEach((fn) => fn.apply(this, eventData))
  }
}

// const testObserver = new Observer<{
//   test: {
//     count: number
//   }
// }>()

// testObserver.on('test', (data) => {
//   data.count
// })

export default Observer
