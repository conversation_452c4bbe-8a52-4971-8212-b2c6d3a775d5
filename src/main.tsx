import React from 'react'
import './env'
import { RouterProvider } from 'react-router-dom'
import { MDXProvider } from '@mdx-js/react'
import ReactDOM from 'react-dom/client'
import { Provider } from 'react-redux'
import { store } from '@/store'
import Popup from '@/features/popup'
import { component } from '@/components/mdx'
import routes from './routes'
import './assets/icons/style.css'
import './index.css'

window.React = React
// @ts-ignore
window.ReactDOM = ReactDOM

// @ts-ignore
if (!window.global) window.global = {}

ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
  <Provider store={store}>
    <MDXProvider components={component}>
      <RouterProvider router={routes} />
      <Popup />
    </MDXProvider>
  </Provider>
)
