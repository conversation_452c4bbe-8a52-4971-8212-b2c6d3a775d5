import { ColumnDef } from '@tanstack/react-table'

export type <PERSON><PERSON><PERSON><PERSON><PERSON><RecordType, DataIndex extends keyof RecordType> =
  (params: {
    value: RecordType[DataIndex]
    data: RecordType
    setData: (data: RecordType) => void
    dataIndex: DataIndex
    rowIdx: number
  }) => React.ReactElement

export type TableColumn<RecordType, DataIndex extends keyof RecordType = keyof RecordType> = {
  title: string | React.ReactNode
  key: string
  width?: number
  dataIndex: DataIndex
  render?: RenderHandler<RecordType, DataIndex>
} & Omit<ColumnDef<RecordType>, 'header' | 'accessorKey' | 'id' | 'cell'>
