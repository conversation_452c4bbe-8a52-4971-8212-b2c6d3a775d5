import { TaskData } from './types'

// ==================== 数据定义 ====================
export const defaultData: TaskData[] = [
  {
    taskName: '确定考核周期',
    status: '进行中',
    assignee: { name: '况民振', avatar: 'https://i.pravatar.cc/150?u=a01' },
    priority: 'P1 最高',
    team: '核心团队',
  },
  {
    taskName: '明确考核目的',
    status: '进行中',
    assignee: { name: '鱼小溪', avatar: 'https://i.pravatar.cc/150?u=a02' },
    priority: 'P1 最高',
    team: '核心团队',
  },
  {
    taskName: '选择考核方法',
    status: '进行中',
    assignee: { name: '军', avatar: 'https://i.pravatar.cc/150?u=a03' },
    priority: 'P3 中',
    team: '支持团队',
  },
  {
    taskName: '制定考核标准',
    status: '进行中',
    assignee: { name: '关超', avatar: 'https://i.pravatar.cc/150?u=a04' },
    priority: 'P3 中',
    team: '支持团队',
  },
  {
    taskName: '设计登录页面',
    status: '已完成',
    assignee: { name: '张三', avatar: 'https://i.pravatar.cc/150?u=a05' },
    priority: 'P2 高',
    team: '核心团队',
  },
  {
    taskName: '后端 API 开发',
    status: '已完成',
    assignee: { name: '李四', avatar: 'https://i.pravatar.cc/150?u=a06' },
    priority: 'P1 最高',
    team: '核心团队',
  },
  {
    taskName: '修复支付 Bug',
    status: '待办',
    assignee: { name: '王五', avatar: 'https://i.pravatar.cc/150?u=a07' },
    priority: 'P1 最高',
    team: '支持团队',
  },
  {
    taskName: 'UI/UX 设计评审',
    status: '待办',
    assignee: { name: '赵六', avatar: 'https://i.pravatar.cc/150?u=a08' },
    priority: 'P3 中',
    team: '设计团队',
  },
  {
    taskName: '部署到测试环境',
    status: '已完成',
    assignee: { name: '孙七', avatar: 'https://i.pravatar.cc/150?u=a09' },
    priority: 'P4 低',
    team: '运维团队',
  },
]
