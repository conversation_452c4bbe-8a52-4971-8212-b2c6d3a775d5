// import { faker } from '@faker-js/faker'
import { ColumnDef } from '@tanstack/react-table'
import Mock from 'mockjs'

export type Person = {
  firstName: string
  lastName: string
  age: number
  visits: number
  progress: number
  status: 'relationship' | 'complicated' | 'single'
  subRows?: Person[]
}

const range = (len: number) => {
  const arr: number[] = []
  for (let i = 0; i < len; i++) {
    arr.push(i)
  }
  return arr
}

// const newPerson = (): Person => {
//   return {
//     firstName: faker.person.firstName(),
//     lastName: faker.person.lastName(),
//     age: faker.number.int(40),
//     visits: faker.number.int(1000),
//     progress: faker.number.int(100),
//     status: faker.helpers.shuffle<Person['status']>([
//       'relationship',
//       'complicated',
//       'single',
//     ])[0]!,
//   }
// }

const newPersonMock = (): Person => {
  const data = Mock.mock({
    'datas|10': [
      {
        firstName: '@first',
        lastName: '@last',
        age: '@integer(18, 60)',
        visits: '@integer(100, 1000)',
        progress: '@integer(0, 100)',
        status: '@pick(["relationship", "complicated", "single"])',
      },
    ],
  })
  console.log('data', data)
  return data.datas
}

export function makeData(...lens: number[]) {
  return newPersonMock();

  const makeDataLevel = (depth = 0): Person[] => {
    const len = lens[depth]!
    return range(len).map((d): Person => {
      return {
        ...newPersonMock(),
        subRows: lens[depth + 1] ? makeDataLevel(depth + 1) : undefined,
      }
    })
  }

  return makeDataLevel()
}

export const makeColumns = num =>
  [...Array(num)].map<ColumnDef<Person>>((_, i) => {
    let pinProps: Partial<ColumnDef<Person>> = {};
    if (i === 0) {
      pinProps = {
        pin: 'left',
      }
    }
    return {
      ...pinProps,
      accessorKey: i.toString(),
      header: 'Column ' + i.toString(),
      size: Math.floor(Math.random() * 150) + 100,
    }
  })

export const makeRows = (num, columns) =>
  [...Array(num)].map(() => ({
    ...Object.fromEntries(
      columns.map(col => [col.accessorKey, Mock.mock('@first')])
    ),
  }))