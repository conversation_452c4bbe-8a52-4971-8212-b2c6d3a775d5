import React from 'react'
import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  getExpandedRowModel,
  getGroupedRowModel,
  useReactTable,
} from '@tanstack/react-table'

// ==================== 类型定义 ====================
interface TaskData {
  taskName: string
  status: string
  assignee: { name: string; avatar: string }
  priority: string
  team: string
}

interface GroupingControlsProps {
  grouping: string[]
  groupableColumns: string[]
  toggleGrouping: (columnId: string) => void
  setGrouping: (grouping: string[]) => void
}

interface TableContainerProps {
  table: any
  grouping: string[]
  rows: any[]
}

// ==================== 数据定义 ====================
const defaultData: TaskData[] = [
  {
    taskName: '确定考核周期',
    status: '进行中',
    assignee: { name: '况民振', avatar: 'https://i.pravatar.cc/150?u=a01' },
    priority: 'P1 最高',
    team: '核心团队',
  },
  {
    taskName: '明确考核目的',
    status: '进行中',
    assignee: { name: '鱼小溪', avatar: 'https://i.pravatar.cc/150?u=a02' },
    priority: 'P1 最高',
    team: '核心团队',
  },
  {
    taskName: '选择考核方法',
    status: '进行中',
    assignee: { name: '军', avatar: 'https://i.pravatar.cc/150?u=a03' },
    priority: 'P3 中',
    team: '支持团队',
  },
  {
    taskName: '制定考核标准',
    status: '进行中',
    assignee: { name: '关超', avatar: 'https://i.pravatar.cc/150?u=a04' },
    priority: 'P3 中',
    team: '支持团队',
  },
  {
    taskName: '设计登录页面',
    status: '已完成',
    assignee: { name: '张三', avatar: 'https://i.pravatar.cc/150?u=a05' },
    priority: 'P2 高',
    team: '核心团队',
  },
  {
    taskName: '后端 API 开发',
    status: '已完成',
    assignee: { name: '李四', avatar: 'https://i.pravatar.cc/150?u=a06' },
    priority: 'P1 最高',
    team: '核心团队',
  },
  {
    taskName: '修复支付 Bug',
    status: '待办',
    assignee: { name: '王五', avatar: 'https://i.pravatar.cc/150?u=a07' },
    priority: 'P1 最高',
    team: '支持团队',
  },
  {
    taskName: 'UI/UX 设计评审',
    status: '待办',
    assignee: { name: '赵六', avatar: 'https://i.pravatar.cc/150?u=a08' },
    priority: 'P3 中',
    team: '设计团队',
  },
  {
    taskName: '部署到测试环境',
    status: '已完成',
    assignee: { name: '孙七', avatar: 'https://i.pravatar.cc/150?u=a09' },
    priority: 'P4 低',
    team: '运维团队',
  },
]

// ==================== 工具函数 ====================
const calculateC = (a: number, b: number): number => {
  const result = a - b - 1
  return Math.max(0, result)
}

const getBorderRadius = (depth: number): string => {
  switch (depth) {
    case 0:
      return '8px'
    case 1:
      return '6px'
    case 2:
      return '4px'
    default:
      return '4px'
  }
}

// ==================== 单元格渲染函数 ====================
const renderTaskNameCell = ({ row, getValue, table }: any) => {
  let serialNumberText = ''
  const parentRow = row.getParentRow()
  
  if (parentRow) {
    const siblingDataRows = parentRow.subRows.filter((d: any) => !d.getIsGrouped())
    const serialIndex = siblingDataRows.findIndex((d: any) => d.id === row.id)
    if (serialIndex !== -1) {
      serialNumberText = `${serialIndex + 1}.`
    }
  } else {
    const topLevelDataRows = table.getPreGroupedRowModel().rows
    const serialIndex = topLevelDataRows.findIndex((d: any) => d.id === row.id)
    if (serialIndex !== -1) {
      serialNumberText = `${serialIndex + 1}.`
    }
  }

  return (
    <div className='flex justify-center items-center h-10'>
      <span className='text-gray-500 text-right w-6 pr-2 flex-shrink-0 text-sm'>
        {serialNumberText}
      </span>
      <span className='flex-grow'>{getValue()}</span>
    </div>
  )
}

const renderStatusCell = (info: any) => (
  <span className='px-3 py-1 text-sm font-medium text-blue-800 bg-blue-100 rounded-md'>
    {info.getValue()}
  </span>
)

const renderAssigneeCell = (info: any) => {
  const value = info.getValue()
  if (value && typeof value === 'object' && 'avatar' in value) {
    return (
      <div className='flex items-center gap-2'>
        <img
          src={value.avatar}
          alt={value.name}
          className='w-6 h-6 rounded-full object-cover'
        />
        <span>{value.name}</span>
      </div>
    )
  }
  return value
}

const renderPriorityCell = (info: any) => {
  const priority = info.getValue()
  let colorClass = 'text-gray-800'
  if (priority === 'P1 最高') colorClass = 'text-red-500 font-bold'
  if (priority === 'P2 高') colorClass = 'text-orange-500 font-bold'
  if (priority === 'P3 中') colorClass = 'text-yellow-600 font-bold'
  if (priority === 'P4 低') colorClass = 'text-gray-500 font-bold'
  return <span className={colorClass}>{priority}</span>
}

// ==================== 列配置 ====================
const columnHelper = createColumnHelper<TaskData>()

const columns = [
  columnHelper.accessor('taskName', {
    header: '任务名称',
    cell: renderTaskNameCell,
  }),
  columnHelper.accessor('status', {
    header: '状态',
    cell: renderStatusCell,
  }),
  columnHelper.accessor('assignee', {
    header: '负责人',
    cell: renderAssigneeCell,
  }),
  columnHelper.accessor('priority', {
    header: '优先级',
    cell: renderPriorityCell,
  }),
  columnHelper.accessor('team', { 
    header: '团队' 
  }),
]

// ==================== 业务逻辑 Hook ====================
const useTableState = () => {
  const [data] = React.useState<TaskData[]>(() => [...defaultData])
  const [grouping, setGrouping] = React.useState<string[]>(['status', 'priority'])
  const [columnOrder, setColumnOrder] = React.useState<string[]>(
    columns.map((column) => column.id || '')
  )

  const toggleGrouping = (columnId: string) => {
    setGrouping((prevGrouping) => {
      if (prevGrouping.includes(columnId)) {
        return prevGrouping.filter((id) => id !== columnId)
      }
      return [...prevGrouping, columnId]
    })
  }

  return {
    data,
    grouping,
    setGrouping,
    columnOrder,
    setColumnOrder,
    toggleGrouping,
  }
}

const useTableInstance = (
  data: TaskData[], 
  grouping: string[], 
  columnOrder: string[], 
  setGrouping: any, 
  setColumnOrder: any
) => {
  return useReactTable({
    data,
    columns,
    state: {
      grouping,
      columnOrder,
    },
    groupedColumnMode: false,
    onGroupingChange: setGrouping,
    onColumnOrderChange: setColumnOrder,
    getExpandedRowModel: getExpandedRowModel(),
    getGroupedRowModel: getGroupedRowModel(),
    getCoreRowModel: getCoreRowModel(),
  })
}

// ==================== 分组控制组件 ====================
const GroupingControls: React.FC<GroupingControlsProps> = ({
  grouping,
  groupableColumns,
  toggleGrouping,
  setGrouping,
}) => {
  const getColumnHeader = (colId: string) => {
    const column = columns.find((col) => col.id === colId)
    return typeof column?.header === 'string' ? column.header : colId
  }

  return (
    <div className='mb-6'>
      <h2 className='text-lg font-semibold text-gray-900 mb-4'>任务列表</h2>

      <div className='flex flex-wrap gap-2 mb-4'>
        <span className='text-sm font-medium text-gray-700'>选择分组:</span>
        {groupableColumns.map((colId) => (
          <button
            key={colId}
            onClick={() => toggleGrouping(colId)}
            className={`px-3 py-1 text-sm rounded-md ${
              grouping.includes(colId)
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 text-gray-700'
            }`}
          >
            {getColumnHeader(colId)}
            {grouping.includes(colId) && ' ✓'}
          </button>
        ))}
      </div>

      {grouping.length > 0 && (
        <div className='flex flex-wrap gap-2 mb-4'>
          <span className='text-sm font-medium text-gray-700'>
            当前分组顺序:
          </span>
          {grouping.map((colId, index) => (
            <div
              key={colId}
              className='flex items-center bg-blue-100 text-blue-800 px-3 py-1 rounded-md text-sm'
            >
              <span>
                {index + 1}. {getColumnHeader(colId)}
              </span>
              <button
                onClick={() => toggleGrouping(colId)}
                className='ml-2 text-blue-600 hover:text-blue-800'
              >
                ×
              </button>
            </div>
          ))}
          <button
            onClick={() => setGrouping([])}
            className='px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded-md'
          >
            清除所有分组
          </button>
        </div>
      )}
    </div>
  )
}

// ==================== 表格容器组件 ====================
const TableContainer: React.FC<TableContainerProps> = ({ table, grouping, rows }) => {
  return (
    <div className='overflow-x-auto'>
      <table
        className='min-w-full text-sm border-separate'
        style={{ borderSpacing: '0' }}
      >
        <TableHeader table={table} grouping={grouping} />
        <TableBody table={table} grouping={grouping} rows={rows} />
      </table>
    </div>
  )
}

// ==================== 表头组件 ====================
const TableHeader: React.FC<{ table: any; grouping: string[] }> = ({ table, grouping }) => {
  return (
    <thead className='align-top'>
      {table.getHeaderGroups().map((headerGroup: any) => (
        <tr
          key={headerGroup.id}
          style={{ backgroundColor: '#F7F9FC' }}
        >
          {headerGroup.headers.map((header: any, index: number) => (
            <th
              key={header.id}
              colSpan={header.colSpan}
              className={`px-4 py-3 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider
                ${index === 0 ? 'rounded-tl-lg border-l' : ''}
                ${index === headerGroup.headers.length - 1 ? 'rounded-tr-lg border-r' : ''}
                ${index === 0 && grouping.length > 0 ? 'rounded-bl-lg' : ''}
                ${index === headerGroup.headers.length - 1 && grouping.length > 0 ? 'rounded-br-lg' : ''}
                ${index !== 0 && index !== headerGroup.headers.length - 1 ? 'border-l border-r' : ''}
                border-t border-b border-solid border-[#DFE3EA]`}
            >
              {header.isPlaceholder
                ? null
                : flexRender(header.column.columnDef.header, header.getContext())}
            </th>
          ))}
        </tr>
      ))}
    </thead>
  )
}

// ==================== 表体组件 ====================
const TableBody: React.FC<{ table: any; grouping: string[]; rows: any[] }> = ({
  table,
  grouping,
  rows
}) => {
  // 工具函数：判断指定分组的父层级是否为最后一个
  const isParentLastInGrandParent = (
    currentGroupRow: any,
    currentRowIndex: number
  ) => {
    // 找到父分组
    let parentGroup = null
    for (let i = currentRowIndex - 1; i >= 0; i--) {
      const prevRow = rows[i]
      if (
        prevRow.getIsGrouped() &&
        prevRow.depth === currentGroupRow.depth - 1
      ) {
        parentGroup = prevRow
        break
      }
    }

    if (!parentGroup) return false

    // 查找父分组的下一个同级分组
    for (let i = currentRowIndex + 1; i < rows.length; i++) {
      const nextRow = rows[i]
      if (
        nextRow.getIsGrouped() &&
        nextRow.depth === parentGroup.depth
      ) {
        return false
      }
      if (
        nextRow.getIsGrouped() &&
        nextRow.depth < parentGroup.depth
      ) {
        break
      }
    }
    return true
  }

  // 预先计算每个分组的结束位置
  const groupEndPositions = new Map()
  for (let i = 0; i < rows.length; i++) {
    const currentRow = rows[i]
    if (currentRow.getIsGrouped()) {
      let endPosition = i
      for (let j = i + 1; j < rows.length; j++) {
        const nextRow = rows[j]
        if (
          nextRow.getIsGrouped() &&
          nextRow.depth <= currentRow.depth
        ) {
          break
        }
        if (nextRow.depth > currentRow.depth) {
          endPosition = j
        }
      }
      groupEndPositions.set(currentRow.id, endPosition)
    }
  }

  return (
    <tbody>
      {grouping.length > 0 && <div className='w-full pt-1.5'></div>}
      {rows.map((row: any, rowIndex: number) => (
        <TableRow
          key={row.id}
          row={row}
          rowIndex={rowIndex}
          rows={rows}
          table={table}
          isParentLastInGrandParent={isParentLastInGrandParent}
          groupEndPositions={groupEndPositions}
        />
      ))}
    </tbody>
  )
}

// ==================== 表格行组件 ====================
interface TableRowProps {
  row: any
  rowIndex: number
  rows: any[]
  table: any
  isParentLastInGrandParent: (row: any, index: number) => boolean
  groupEndPositions: Map<string, number>
}

const TableRow: React.FC<TableRowProps> = ({
  row,
  rowIndex,
  rows,
  table,
  isParentLastInGrandParent,
  groupEndPositions,
}) => {
  if (row.getIsGrouped()) {
    return (
      <GroupRow
        row={row}
        rowIndex={rowIndex}
        rows={rows}
        table={table}
        groupEndPositions={groupEndPositions}
      />
    )
  }

  return (
    <DataRow
      row={row}
      rowIndex={rowIndex}
      rows={rows}
      table={table}
      isParentLastInGrandParent={isParentLastInGrandParent}
      groupEndPositions={groupEndPositions}
    />
  )
}

// ==================== 分组行组件 ====================
interface GroupRowProps {
  row: any
  rowIndex: number
  rows: any[]
  table: any
  groupEndPositions: Map<string, number>
}

const GroupRow: React.FC<GroupRowProps> = ({
  row,
  rowIndex,
  rows,
  table,
  groupEndPositions,
}) => {
  const borderRadius = getBorderRadius(row.depth)
  const isExpanded = row.getIsExpanded()

  // 判断是否为第一层分组的最后一个分组
  const isLastFirstLevelGroup = (() => {
    if (row.depth !== 0) return false
    const firstLevelGroups = rows.filter(
      (r: any) => r.getIsGrouped() && r.depth === 0
    )
    const currentGroupIndex = firstLevelGroups.findIndex(
      (g: any) => g.id === row.id
    )
    return currentGroupIndex === firstLevelGroups.length - 1
  })()

  // 判断是否为父分组内的最后一个子分组
  const isLastSubGroupInParent = (() => {
    if (row.depth === 0) return false
    for (let i = rowIndex + 1; i < rows.length; i++) {
      const nextRow = rows[i]
      if (nextRow.getIsGrouped()) {
        if (nextRow.depth < row.depth) {
          return true
        }
        if (nextRow.depth === row.depth) {
          return false
        }
      }
    }
    return true
  })()

  return (
    <>
      <tr key={row.id}>
        <td
          colSpan={table.getAllLeafColumns().length}
          className='p-0'
        >
          <div className='flex relative'>
            {/* 渲染父级层级的左边框指示线 */}
            {row.depth > 0 && (
              <div className='absolute left-0 top-0 bottom-0 flex items-stretch'>
                {Array.from({ length: row.depth }).map((_, index) => (
                  <div
                    key={index}
                    className='border-l border-solid border-[#E5E7EB]'
                    style={{
                      marginRight: '23px',
                      height: '100%',
                    }}
                  />
                ))}
              </div>
            )}

            {/* 渲染父级层级的右边框指示线 */}
            {row.depth > 0 && (
              <div className='absolute right-0 top-0 bottom-0 flex items-stretch'>
                {Array.from({ length: row.depth }).map((_, index) => (
                  <div
                    key={index}
                    className='border-r border-solid border-[#E5E7EB]'
                    style={{
                      marginLeft: '24px',
                      height: '100%',
                    }}
                  />
                ))}
              </div>
            )}

            <div
              className={`flex items-center font-medium text-gray-700 w-full border-solid border-[#E5E7EB] py-2 px-3 relative ${
                isExpanded
                  ? 'border-t border-l border-r'
                  : 'border'
              }`}
              style={{
                borderRadius: isExpanded
                  ? `${borderRadius} ${borderRadius} 0 0`
                  : borderRadius,
                marginLeft: `${row.depth * 24}px`,
                marginRight: `${row.depth * 24}px`,
                marginBottom: isExpanded
                  ? '0'
                  : isLastFirstLevelGroup || isLastSubGroupInParent
                  ? '0'
                  : '8px',
              }}
            >
              <button
                onClick={row.getToggleExpandedHandler()}
                className='text-sm p-1 mr-2 hover:bg-gray-200 rounded'
              >
                {isExpanded ? '▼' : '▶'}
              </button>
              <span className='text-xs font-normal bg-white text-gray-600 px-2 py-1 rounded border'>
                L{row.depth + 1}
              </span>
              <span className='ml-2 flex-1'>
                {String(row.groupingValue)}
              </span>
              <span className='text-gray-500 font-normal text-xs bg-white px-2 py-1 rounded border'>
                {row.subRows.length}条
              </span>
            </div>
          </div>
        </td>
      </tr>

      {/* 分组结束的占位行 */}
      <GroupEndRows
        row={row}
        rowIndex={rowIndex}
        rows={rows}
        table={table}
        groupEndPositions={groupEndPositions}
      />
    </>
  )
}

// ==================== 数据行组件 ====================
interface DataRowProps {
  row: any
  rowIndex: number
  rows: any[]
  table: any
  isParentLastInGrandParent: (row: any, index: number) => boolean
  groupEndPositions: Map<string, number>
}

const DataRow: React.FC<DataRowProps> = ({
  row,
  rowIndex,
  rows,
  table,
  isParentLastInGrandParent,
  groupEndPositions,
}) => {
  // 判断是否为分组内的最后一行
  const isLastRowInGroup = (() => {
    if (rowIndex === rows.length - 1) return true
    const nextRow = rows[rowIndex + 1]
    if (!nextRow) return true
    if (!row.getIsGrouped() && nextRow.getIsGrouped()) return true
    return false
  })()

  return (
    <>
      <tr key={row.id} className='relative'>
        {row.getVisibleCells().map((cell: any, cellIndex: number) => {
          const isFirstCell = cellIndex === 0
          const isLastCell = cellIndex === row.getVisibleCells().length - 1

          return (
            <td
              key={cell.id}
              className='align-middle relative bg-white'
              style={{ position: 'relative' }}
            >
              {/* 单元格边框 */}
              <CellBorder
                row={row}
                rowIndex={rowIndex}
                rows={rows}
                isFirstCell={isFirstCell}
                isLastCell={isLastCell}
                isLastRowInGroup={isLastRowInGroup}
              />

              {/* 层级指示线 */}
              <LevelIndicators
                row={row}
                isFirstCell={isFirstCell}
                isLastCell={isLastCell}
              />

              {/* 单元格内容 */}
              <div
                style={{
                  marginLeft:
                    isFirstCell && row.depth > 0
                      ? `${row.depth * 24}px`
                      : '0',
                }}
              >
                {flexRender(
                  cell.column.columnDef.cell,
                  cell.getContext()
                )}
              </div>
            </td>
          )
        })}
      </tr>

      {/* 数据行结束后的占位行 */}
      <DataRowEndSpacing
        row={row}
        rowIndex={rowIndex}
        rows={rows}
        table={table}
        isParentLastInGrandParent={isParentLastInGrandParent}
        groupEndPositions={groupEndPositions}
      />
    </>
  )
}

// ==================== 单元格边框组件 ====================
interface CellBorderProps {
  row: any
  rowIndex: number
  rows: any[]
  isFirstCell: boolean
  isLastCell: boolean
  isLastRowInGroup: boolean
}

const CellBorder: React.FC<CellBorderProps> = ({
  row,
  rowIndex,
  rows,
  isFirstCell,
  isLastCell,
  isLastRowInGroup,
}) => {
  const prevRow = rows[rowIndex - 1]
  const isFirstDataRow =
    !prevRow ||
    prevRow.getIsGrouped() ||
    prevRow.depth < row.depth

  const borderRadius = getBorderRadius(row.depth)
  const leftOffset =
    isFirstCell && row.depth > 0
      ? `${row.depth * 24 - 24}px`
      : '0'
  const rightOffset =
    isLastCell && row.depth > 0
      ? `${row.depth * 24 - 24}px`
      : '0'

  return (
    <>
      {/* 主边框容器 */}
      <div
        className='absolute inset-0 pointer-events-none'
        style={{
          left: leftOffset,
          right: rightOffset,
          border: '1px solid #DFE3EA',
          borderTop: isFirstDataRow
            ? '1px solid #DFE3EA'
            : 'none',
          ...(isLastRowInGroup && {
            borderBottomLeftRadius: isFirstCell
              ? borderRadius
              : '0',
            borderBottomRightRadius: isLastCell
              ? borderRadius
              : '0',
          }),
          borderLeft: isFirstCell
            ? '1px solid #DFE3EA'
            : 'none',
          borderRight: isLastCell
            ? '1px solid #DFE3EA'
            : 'none',
        }}
      />

      {/* 单元格间的垂直分隔线 */}
      {!isLastCell && (
        <div className='absolute top-0 bottom-0 right-0 w-px bg-[#DFE3EA] pointer-events-none' />
      )}
    </>
  )
}

// ==================== 层级指示线组件 ====================
interface LevelIndicatorsProps {
  row: any
  isFirstCell: boolean
  isLastCell: boolean
}

const LevelIndicators: React.FC<LevelIndicatorsProps> = ({
  row,
  isFirstCell,
  isLastCell,
}) => {
  return (
    <>
      {/* 左侧层级指示线 */}
      {isFirstCell && row.depth > 0 && (
        <div className='absolute left-0 top-0 bottom-0 flex items-stretch pointer-events-none'>
          {Array.from({ length: row.depth - 1 }).map((_, index) => (
            <div
              key={index}
              className='border-l border-solid border-[#DFE3EA]'
              style={{
                marginRight: '23px',
                height: '100%',
              }}
            />
          ))}
        </div>
      )}

      {/* 右侧层级指示线 */}
      {isLastCell && row.depth > 0 && (
        <div className='absolute right-0 top-0 bottom-0 flex items-stretch pointer-events-none'>
          {Array.from({ length: row.depth - 1 }).map((_, index) => (
            <div
              key={index}
              className='border-r border-solid border-[#DFE3EA]'
              style={{
                marginLeft: '23px',
                height: '100%',
              }}
            />
          ))}
        </div>
      )}
    </>
  )
}

// ==================== 分组结束行组件 ====================
interface GroupEndRowsProps {
  row: any
  rowIndex: number
  rows: any[]
  table: any
  groupEndPositions: Map<string, number>
}

const GroupEndRows: React.FC<GroupEndRowsProps> = ({
  row,
  rowIndex,
  rows,
  table,
  groupEndPositions,
}) => {
  // 检查当前分组行是否是某个父级分组的结束位置（不包括自己）
  const groupsToEnd = []
  for (const [groupId, endPosition] of groupEndPositions) {
    if (endPosition === rowIndex && groupId !== row.id) {
      const groupRow = rows.find((r: any) => r.id === groupId)
      if (groupRow && groupRow.depth < row.depth) {
        groupsToEnd.push(groupRow)
      }
    }
  }

  return (
    <>
      {groupsToEnd.map((groupRow: any, gIndex: number) => (
        <tr key={`group-end-${groupRow.id}`}>
          <td
            colSpan={table.getAllLeafColumns().length}
            className={`p-0 ${
              gIndex === groupsToEnd.length - 1 ? 'pb-2' : ''
            } ${
              row.depth == 1 || gIndex > 0
                ? ''
                : 'border-l border-r border-solid border-[#DFE3EA]'
            }`}
          >
            <div className='w-full flex'>
              {Array.from({
                length: calculateC(row.depth, gIndex),
              }).map((_, boxIndex) => (
                <div
                  key={boxIndex}
                  className={`${
                    boxIndex < 1 ? '' : 'border-l'
                  } border-solid border-[#DFE3EA] w-[23px] h-2`}
                />
              ))}
              <div
                className={`flex-1 p-0 border-l border-r border-b border-solid border-[#DFE3EA] ${
                  row.depth < 0 ? 'mx-6' : ''
                }`}
                style={{
                  height: `8px`,
                  borderRadius: `0 0 ${6 + row.depth * 2}px ${
                    6 + row.depth * 2
                  }px`,
                }}
              />
              {Array.from({
                length: calculateC(row.depth, gIndex),
              }).map((_, boxIndex) => (
                <div
                  key={boxIndex}
                  className={`${
                    boxIndex < 1 ? '' : 'border-r'
                  } border-solid border-[#DFE3EA] w-[23px] h-2`}
                />
              ))}
            </div>
          </td>
        </tr>
      ))}
    </>
  )
}

// ==================== 数据行结束间距组件 ====================
interface DataRowEndSpacingProps {
  row: any
  rowIndex: number
  rows: any[]
  table: any
  isParentLastInGrandParent: (row: any, index: number) => boolean
  groupEndPositions: Map<string, number>
}

const DataRowEndSpacing: React.FC<DataRowEndSpacingProps> = ({
  row,
  rowIndex,
  rows,
  table,
  isParentLastInGrandParent,
  groupEndPositions,
}) => {
  // 判断是否为分组内的最后一行数据
  const isLastDataRowInGroup = (() => {
    if (rowIndex === rows.length - 1) return true
    const nextRow = rows[rowIndex + 1]
    if (!nextRow) return true
    if (!row.getIsGrouped() && nextRow.getIsGrouped()) return true
    return false
  })()

  // 小于等于2层级的最后一行数据行后添加tr
  const shouldAddSpacing = row.depth <= 2 && isLastDataRowInGroup

  // 检查当前数据行是否是某个分组的最后一个元素
  const groupsToEnd = []
  for (const [groupId, endPosition] of groupEndPositions) {
    if (endPosition === rowIndex) {
      const groupRow = rows.find((r: any) => r.id === groupId)
      if (groupRow && groupRow.depth > 1) {
        const parentIsLast = isParentLastInGrandParent(groupRow, rowIndex)
        groupsToEnd.push({ groupRow, parentIsLast })
      }
    }
  }

  return (
    <>
      {shouldAddSpacing && (
        <tr>
          <td
            colSpan={table.getAllLeafColumns().length}
            className={`p-0 ${
              row.depth === 2 && isParentLastInGrandParent(row, rowIndex) ? 'pb-2' : ''
            }`}
          >
            {(() => {
              if (row.depth === 1) {
                return <div className='h-2' />
              } else if (row.depth === 2) {
                const parentIsLast = isParentLastInGrandParent(row, rowIndex)
                if (parentIsLast) {
                  return <div className='h-2 border-l border-r border-b border-solid border-[#DFE3EA] rounded-br-[8px] rounded-bl-[8px]' />
                } else {
                  return <div className='h-2 border-l border-r border-solid border-[#DFE3EA]' />
                }
              }
            })()}
          </td>
        </tr>
      )}

      {/* 分组结束的占位边框 */}
      {groupsToEnd.map((item, gIndex) => (
        <React.Fragment key={`group-end-${item.groupRow.id}`}>
          <tr>
            <td
              colSpan={table.getAllLeafColumns().length}
              className={`p-0 border-solid border-[#DFE3EA] ${
                row.depth < 0
                  ? 'border-l border-r'
                  : 'border-l border-r'
              }`}
            >
              <div className='flex w-full'>
                {Array.from({
                  length: row.depth - gIndex - 2,
                }).map((_, boxIndex) => (
                  <div
                    key={boxIndex}
                    className={`${
                      boxIndex < 1 ? '' : 'border-l'
                    } border-solid border-[#DFE3EA] w-[23px] h-2`}
                  />
                ))}
                <div
                  className={`flex-1 p-0 border-l border-r border-b border-solid border-[#DFE3EA] ${
                    row.depth < 0 ? 'mx-6' : ''
                  }`}
                  style={{
                    height: `8px`,
                    borderRadius: `0 0 ${6 + row.depth}px ${
                      6 + row.depth
                    }px`,
                  }}
                />
                {Array.from({
                  length: row.depth - gIndex - 2,
                }).map((_, boxIndex) => (
                  <div
                    key={boxIndex}
                    className={`${
                      boxIndex < 1 ? '' : 'border-r'
                    } border-solid border-[#DFE3EA] w-[23px] h-2`}
                  />
                ))}
              </div>
            </td>
          </tr>
          <tr className='h-2'>
            <td
              className={`h-full p-0 ${item.parentIsLast ? 'pb-2' : ''}`}
              colSpan={5}
            >
              <div
                className={`border-solid border-[#DFE3EA] border-l border-r w-full h-[8px] ${
                  item.parentIsLast ? 'border-b' : ''
                }`}
                style={{
                  borderRadius: item.parentIsLast
                    ? `0 0 ${6 + row.depth}px ${6 + row.depth}px`
                    : 'none',
                }}
              />
            </td>
          </tr>
        </React.Fragment>
      ))}
    </>
  )
}

// ==================== 主组件 ====================
function GroupableTaskTable() {
  const { data, grouping, setGrouping, columnOrder, setColumnOrder, toggleGrouping } = useTableState()
  const table = useTableInstance(data, grouping, columnOrder, setGrouping, setColumnOrder)
  const { rows } = table.getRowModel()
  const groupableColumns = ['status', 'priority', 'team', 'assignee']

  return (
    <div className='p-4 sm:p-6 lg:p-8 font-sans min-h-screen'>
      <GroupingControls
        grouping={grouping}
        groupableColumns={groupableColumns}
        toggleGrouping={toggleGrouping}
        setGrouping={setGrouping}
      />
      <TableContainer table={table} grouping={grouping} rows={rows} />
    </div>
  )
}

export default GroupableTaskTable
