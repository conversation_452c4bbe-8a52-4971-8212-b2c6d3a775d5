// src/components/GroupableTaskTable.js

import React from 'react'
import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  getExpandedRowModel,
  getGroupedRowModel,
  useReactTable,
} from '@tanstack/react-table'

// 示例数据
const defaultData = [
  {
    taskName: '确定考核周期',
    status: '进行中',
    assignee: { name: '况民振', avatar: 'https://i.pravatar.cc/150?u=a01' },
    priority: 'P1 最高',
    team: '核心团队',
  },
  {
    taskName: '明确考核目的',
    status: '进行中',
    assignee: { name: '鱼小溪', avatar: 'https://i.pravatar.cc/150?u=a02' },
    priority: 'P1 最高',
    team: '核心团队',
  },
  {
    taskName: '选择考核方法',
    status: '进行中',
    assignee: { name: '军', avatar: 'https://i.pravatar.cc/150?u=a03' },
    priority: 'P3 中',
    team: '支持团队',
  },
  {
    taskName: '制定考核标准',
    status: '进行中',
    assignee: { name: '关超', avatar: 'https://i.pravatar.cc/150?u=a04' },
    priority: 'P3 中',
    team: '支持团队',
  },
  {
    taskName: '设计登录页面',
    status: '已完成',
    assignee: { name: '张三', avatar: 'https://i.pravatar.cc/150?u=a05' },
    priority: 'P2 高',
    team: '核心团队',
  },
  {
    taskName: '后端 API 开发',
    status: '已完成',
    assignee: { name: '李四', avatar: 'https://i.pravatar.cc/150?u=a06' },
    priority: 'P1 最高',
    team: '核心团队',
  },
  {
    taskName: '修复支付 Bug',
    status: '待办',
    assignee: { name: '王五', avatar: 'https://i.pravatar.cc/150?u=a07' },
    priority: 'P1 最高',
    team: '支持团队',
  },
  {
    taskName: 'UI/UX 设计评审',
    status: '待办',
    assignee: { name: '赵六', avatar: 'https://i.pravatar.cc/150?u=a08' },
    priority: 'P3 中',
    team: '设计团队',
  },
  {
    taskName: '部署到测试环境',
    status: '已完成',
    assignee: { name: '孙七', avatar: 'https://i.pravatar.cc/150?u=a09' },
    priority: 'P4 低',
    team: '运维团队',
  },
]

const columnHelper = createColumnHelper()

const calculateC = (a: number, b: number) => {
  const result = a - b - 1
  return Math.max(0, result)
}

const columns = [
  columnHelper.accessor('taskName', {
    header: '任务名称',
    cell: ({ row, getValue, table }) => {
      let serialNumberText = ''
      const parentRow = row.getParentRow()
      if (parentRow) {
        const siblingDataRows = parentRow.subRows.filter(
          (d) => !d.getIsGrouped()
        )
        const serialIndex = siblingDataRows.findIndex((d) => d.id === row.id)
        if (serialIndex !== -1) {
          serialNumberText = `${serialIndex + 1}.`
        }
      } else {
        const topLevelDataRows = table.getPreGroupedRowModel().rows
        const serialIndex = topLevelDataRows.findIndex((d) => d.id === row.id)
        if (serialIndex !== -1) {
          serialNumberText = `${serialIndex + 1}.`
        }
      }

      return (
        <div className='flex justify-center items-center h-10 '>
          <span className='text-gray-500 text-right w-6 pr-2 flex-shrink-0 text-sm '>
            {serialNumberText}
          </span>
          <span className='flex-grow'>{getValue()}</span>
        </div>
      )
    },
  }),
  columnHelper.accessor('status', {
    header: '状态',
    cell: (info) => (
      <span className='px-3 py-1 text-sm font-medium text-blue-800 bg-blue-100 rounded-md'>
        {info.getValue()}
      </span>
    ),
  }),
  columnHelper.accessor('assignee', {
    header: '负责人',
    cell: (info) => {
      const value = info.getValue() as any
      if (value && typeof value === 'object' && 'avatar' in value) {
        return (
          <div className='flex items-center gap-2'>
            <img
              src={value.avatar}
              alt={value.name}
              className='w-6 h-6 rounded-full object-cover'
            />
            <span>{value.name}</span>
          </div>
        )
      }
      return value
    },
  }),
  columnHelper.accessor('priority', {
    header: '优先级',
    cell: (info) => {
      const priority = info.getValue()
      let colorClass = 'text-gray-800'
      if (priority === 'P1 最高') colorClass = 'text-red-500 font-bold'
      if (priority === 'P2 高') colorClass = 'text-orange-500 font-bold'
      if (priority === 'P3 中') colorClass = 'text-yellow-600 font-bold'
      if (priority === 'P4 低') colorClass = 'text-gray-500 font-bold'
      return <span className={colorClass}>{priority}</span>
    },
  }),
  columnHelper.accessor('team', { header: '团队' }),
]

function GroupableTaskTable() {
  const [data] = React.useState(() => [...defaultData])
  const [grouping, setGrouping] = React.useState<string[]>([
    'status',
    'priority',
  ])
  const [columnOrder, setColumnOrder] = React.useState(
    columns.map((column) => column.id)
  )

  const toggleGrouping = (columnId: string) => {
    setGrouping((prevGrouping) => {
      if (prevGrouping.includes(columnId)) {
        return prevGrouping.filter((id) => id !== columnId)
      }
      return [...prevGrouping, columnId]
    })
  }

  const table = useReactTable({
    data,
    columns,
    state: {
      grouping,
      columnOrder,
    },
    groupedColumnMode: false,
    onGroupingChange: setGrouping,
    onColumnOrderChange: setColumnOrder,
    getExpandedRowModel: getExpandedRowModel(),
    getGroupedRowModel: getGroupedRowModel(),
    getCoreRowModel: getCoreRowModel(),
  })

  const { rows } = table.getRowModel()
  const groupableColumns = ['status', 'priority', 'team', 'assignee']

  return (
    <div className='p-4 sm:p-6 lg:p-8 font-sans min-h-screen'>
      <div className='mb-6'>
        <h2 className='text-lg font-semibold text-gray-900 mb-4'>任务列表</h2>

        <div className='flex flex-wrap gap-2 mb-4'>
          <span className='text-sm font-medium text-gray-700'>选择分组:</span>
          {groupableColumns.map((colId) => (
            <button
              key={colId}
              onClick={() => toggleGrouping(colId)}
              className={`px-3 py-1 text-sm rounded-md ${
                grouping.includes(colId)
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700'
              }`}
            >
              {columns.find((col) => col.id === colId)?.header || colId}
              {grouping.includes(colId) && ' ✓'}
            </button>
          ))}
        </div>

        {grouping.length > 0 && (
          <div className='flex flex-wrap gap-2 mb-4'>
            <span className='text-sm font-medium text-gray-700'>
              当前分组顺序:
            </span>
            {grouping.map((colId, index) => (
              <div
                key={colId}
                className='flex items-center bg-blue-100 text-blue-800 px-3 py-1 rounded-md text-sm'
              >
                <span>
                  {index + 1}.{' '}
                  {columns.find((col) => col.id === colId)?.header || colId}
                </span>
                <button
                  onClick={() => toggleGrouping(colId)}
                  className='ml-2 text-blue-600 hover:text-blue-800'
                >
                  ×
                </button>
              </div>
            ))}
            <button
              onClick={() => setGrouping([])}
              className='px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded-md'
            >
              清除所有分组
            </button>
          </div>
        )}
      </div>

      <div className='overflow-x-auto'>
        <table
          className='min-w-full text-sm border-separate'
          style={{ borderSpacing: '0' }}
        >
          <thead className='align-top'>
            {table.getHeaderGroups().map((headerGroup) => (
              <tr
                key={headerGroup.id}
                style={{
                  backgroundColor: '#F7F9FC',
                }}
              >
                {headerGroup.headers.map((header, index) => (
                  <th
                    key={header.id}
                    colSpan={header.colSpan}
                    className={`px-4 py-3 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider
            ${index === 0 ? 'rounded-tl-lg border-l' : ''}
            ${
              index === headerGroup.headers.length - 1
                ? 'rounded-tr-lg border-r'
                : ''
            }
            ${index === 0 && grouping.length > 0 ? 'rounded-bl-lg' : ''}
            ${
              index === headerGroup.headers.length - 1 && grouping.length > 0
                ? 'rounded-br-lg'
                : ''
            }
            ${
              index !== 0 && index !== headerGroup.headers.length - 1
                ? 'border-l border-r'
                : ''
            }
            border-t border-b border-solid border-[#DFE3EA]`}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody>
            {grouping.length > 0 && <div className='w-full pt-1.5'></div>}
            {(() => {
              // 通用函数：判断指定分组的父层级是否为最后一个
              const isParentLastInGrandParent = (
                currentGroupRow: any,
                currentRowIndex: number
              ) => {
                // 找到父分组
                let parentGroup = null
                for (let i = currentRowIndex - 1; i >= 0; i--) {
                  const prevRow = rows[i]
                  if (
                    prevRow.getIsGrouped() &&
                    prevRow.depth === currentGroupRow.depth - 1
                  ) {
                    parentGroup = prevRow
                    break
                  }
                }

                if (!parentGroup) return false // 没有父分组

                // 查找父分组的下一个同级分组
                for (let i = currentRowIndex + 1; i < rows.length; i++) {
                  const nextRow = rows[i]
                  if (
                    nextRow.getIsGrouped() &&
                    nextRow.depth === parentGroup.depth
                  ) {
                    return false // 找到父分组的同级分组，说明父分组不是最后一个
                  }
                  // 如果遇到更浅层级的分组，停止查找
                  if (
                    nextRow.getIsGrouped() &&
                    nextRow.depth < parentGroup.depth
                  ) {
                    break
                  }
                }
                return true // 没有找到父分组的同级分组，说明父分组是最后一个
              }

              // 预先计算每个分组的结束位置
              const groupEndPositions = new Map()

              // 遍历所有行，找到每个分组的结束位置
              for (let i = 0; i < rows.length; i++) {
                const currentRow = rows[i]
                if (currentRow.getIsGrouped()) {
                  // 找到这个分组的结束位置
                  let endPosition = i // 默认结束位置是分组本身

                  // 向后查找，找到属于这个分组的最后一个元素（可能是数据行或子分组）
                  for (let j = i + 1; j < rows.length; j++) {
                    const nextRow = rows[j]

                    // 如果遇到同级或更浅的分组，说明当前分组结束了
                    if (
                      nextRow.getIsGrouped() &&
                      nextRow.depth <= currentRow.depth
                    ) {
                      break
                    }

                    // 如果是属于当前分组的元素（数据行或子分组）
                    if (nextRow.depth > currentRow.depth) {
                      endPosition = j
                    }
                  }

                  // 总是设置结束位置，即使没有子元素也要在分组本身后面添加绿色tr
                  groupEndPositions.set(currentRow.id, endPosition)
                  console.log(
                    `分组 ${currentRow.groupingValue} (depth: ${currentRow.depth}) 结束位置: ${endPosition}`
                  )
                }
              }

              return rows.map((row, rowIndex) => {
                // 如果当前行是分组行
                if (row.getIsGrouped()) {
                  // 检查是否是分组的第一个子元素
                  const isFirstGroupElement =
                    rowIndex === 0 ||
                    !rows[rowIndex - 1]?.getIsGrouped() ||
                    rows[rowIndex - 1]?.depth < row.depth
                  // 计算边框圆角大小
                  const getBorderRadius = (depth: number): string => {
                    switch (depth) {
                      case 0:
                        return '8px'
                      case 1:
                        return '6px'
                      case 2:
                        return '4px'
                      default:
                        return '4px'
                    }
                  }

                  const borderRadius = getBorderRadius(row.depth)
                  const isExpanded = row.getIsExpanded()

                  // 判断是否为第一层分组的最后一个分组
                  const isLastFirstLevelGroup = (() => {
                    if (row.depth !== 0) return false // 只检查第一层分组

                    // 收集所有第一层分组
                    const firstLevelGroups = rows.filter(
                      (r) => r.getIsGrouped() && r.depth === 0
                    )
                    const currentGroupIndex = firstLevelGroups.findIndex(
                      (g) => g.id === row.id
                    )
                    return currentGroupIndex === firstLevelGroups.length - 1
                  })()

                  // 判断是否为父分组内的最后一个子分组
                  const isLastSubGroupInParent = (() => {
                    if (row.depth === 0) return false // 第一层分组不需要检查

                    // 查找下一个同级或更浅层级的分组
                    for (let i = rowIndex + 1; i < rows.length; i++) {
                      const nextRow = rows[i]
                      if (nextRow.getIsGrouped()) {
                        // 如果下一个分组的层级小于当前分组，说明当前分组是某个父级的最后一个子分组
                        if (nextRow.depth < row.depth) {
                          return true
                        }
                        // 如果下一个分组的层级等于当前分组，说明还有同级分组，当前不是最后一个
                        if (nextRow.depth === row.depth) {
                          return false
                        }
                        // 如果下一个分组的层级更深，继续查找
                      }
                    }
                    // 如果没有找到同级或更浅的分组，说明是最后一个分组
                    return true
                  })()

                  return (
                    <>
                      <tr key={row.id}>
                        <td
                          colSpan={table.getAllLeafColumns().length}
                          className={`p-0`}
                        >
                          <div className='flex relative'>
                            {/* 渲染父级层级的左边框指示线 */}
                            {row.depth > 0 && (
                              <div className='absolute left-0 top-0 bottom-0 flex items-stretch'>
                                {Array.from({ length: row.depth }).map(
                                  (_, index) => (
                                    <div
                                      key={index}
                                      className='border-l border-solid border-[#E5E7EB]'
                                      style={{
                                        marginRight: '23px',
                                        height: '100%',
                                      }}
                                    />
                                  )
                                )}
                              </div>
                            )}

                            {/* 渲染父级层级的右边框指示线 */}
                            {row.depth > 0 && (
                              <div className='absolute right-0 top-0 bottom-0 flex items-stretch'>
                                {Array.from({ length: row.depth }).map(
                                  (_, index) => (
                                    <div
                                      key={index}
                                      className='border-r border-solid border-[#E5E7EB]'
                                      style={{
                                        marginLeft: '24px',
                                        height: '100%',
                                      }}
                                    />
                                  )
                                )}
                              </div>
                            )}

                            <div
                              className={`flex items-center font-medium text-gray-700 w-full border-solid border-[#E5E7EB] py-2 px-3 relative ${
                                isExpanded
                                  ? 'border-t border-l border-r' // 展开时不显示底边
                                  : 'border' // 未展开时显示所有边
                              }`}
                              style={{
                                borderRadius: isExpanded
                                  ? `${borderRadius} ${borderRadius} 0 0` // 展开时只有上边圆角
                                  : borderRadius, // 未展开时四个角都是圆角
                                marginLeft: `${row.depth * 24}px`,
                                marginRight: `${row.depth * 24}px`,
                                // 使用内联样式控制底部间距
                                marginBottom: isExpanded
                                  ? '0'
                                  : isLastFirstLevelGroup || isLastSubGroupInParent
                                  ? '0'
                                  : '8px',
                              }}
                            >
                              <button
                                onClick={row.getToggleExpandedHandler()}
                                className='text-sm p-1 mr-2 hover:bg-gray-200 rounded'
                              >
                                {isExpanded ? '▼' : '▶'}
                              </button>
                              <span className='text-xs font-normal bg-white text-gray-600 px-2 py-1 rounded border'>
                                L{row.depth + 1}
                              </span>
                              <span className='ml-2 flex-1'>
                                {String(row.groupingValue)}
                              </span>
                              <span className='text-gray-500 font-normal text-xs bg-white px-2 py-1 rounded border'>
                                {row.subRows.length}条
                              </span>
                            </div>
                          </div>
                        </td>
                      </tr>

                      {/* 在分组行后检查是否需要添加分组结束的绿色tr */}
                      {(() => {
                        // 检查当前分组行是否是某个父级分组的结束位置（不包括自己）
                        const groupsToEnd = []
                        for (const [
                          groupId,
                          endPosition,
                        ] of groupEndPositions) {
                          if (endPosition === rowIndex && groupId !== row.id) {
                            // 找到对应的分组行，且必须是更浅层级的分组
                            const groupRow = rows.find((r) => r.id === groupId)
                            if (groupRow && groupRow.depth < row.depth) {
                              groupsToEnd.push(groupRow)
                            }
                          }
                        }

                        //  包裹一
                        return groupsToEnd.map((groupRow, _gIndex) => (
                          <tr key={`group-end-${groupRow.id}`}>
                            <td
                              colSpan={table.getAllLeafColumns().length}
                              className={`p-0 ${
                                // 只有最后一个红色占位tr有pb-2
                                _gIndex === groupsToEnd.length - 1 ? 'pb-2' : ''
                              } ${
                                row.depth == 1 || _gIndex > 0
                                  ? ''
                                  : 'border-l border-r border-solid border-[#DFE3EA]'
                              }`}
                            >
                              <div className='w-full flex'>
                                {Array.from({
                                  length: calculateC(row.depth, _gIndex),
                                }).map((_, boxIndex) => (
                                  <div
                                    key={boxIndex}
                                    className={`${
                                      boxIndex < 1 ? '' : 'border-l'
                                    } border-solid border-[#DFE3EA] w-[23px] h-2`}
                                  />
                                ))}
                                <div
                                  className={`flex-1 p-0 border-l border-r border-b border-solid border-[#DFE3EA] ${
                                    row.depth < 0 ? 'mx-6' : ''
                                  }`}
                                  style={{
                                    height: `8px`,
                                    borderRadius: `0 0 ${6 + row.depth * 2}px ${
                                      6 + row.depth * 2
                                    }px`,
                                  }}
                                />

                                {Array.from({
                                  length: calculateC(row.depth, _gIndex),
                                }).map((_, boxIndex) => (
                                  <div
                                    key={boxIndex}
                                    className={`${
                                      boxIndex < 1 ? '' : 'border-r'
                                    } border-solid border-[#DFE3EA] w-[23px] h-2`}
                                  />
                                ))}
                              </div>
                            </td>
                          </tr>
                        ))
                      })()}
                    </>
                  )
                }

                return (
                  <>
                    <tr key={row.id} className='relative'>
                      {row.getVisibleCells().map((cell, cellIndex) => {
                        const isFirstCell = cellIndex === 0
                        const isLastCell =
                          cellIndex === row.getVisibleCells().length - 1

                        // 构建单元格类名
                        let tdClasses = 'align-middle relative bg-white'

                        return (
                          <td
                            key={cell.id}
                            className={tdClasses}
                            style={{
                              position: 'relative',
                            }}
                          >
                            {/* 单元格边框 - 避免重叠，最后一行加圆角 */}
                            {(() => {
                              const prevRow = rows[rowIndex - 1]
                              const isFirstDataRow =
                                !prevRow ||
                                prevRow.getIsGrouped() ||
                                prevRow.depth < row.depth

                              // 判断是否为分组内的最后一行
                              const isLastRowInGroup = (() => {
                                // 如果是整个表格的最后一行，肯定是最后一行
                                if (rowIndex === rows.length - 1) return true

                                // 检查下一行
                                const nextRow = rows[rowIndex + 1]
                                if (!nextRow) return true

                                // 如果当前行不是分组行，且下一行是分组行，则当前行是分组的最后一行
                                if (
                                  !row.getIsGrouped() &&
                                  nextRow.getIsGrouped()
                                )
                                  return true

                                return false
                              })()

                              // 根据层级计算圆角大小 - 增大圆角便于观察
                              const getBorderRadius = (
                                depth: number
                              ): string => {
                                switch (depth) {
                                  case 0:
                                    return '8px' // 增大到16px便于观察
                                  case 1:
                                    return '6px' // 增大到12px便于观察
                                  case 2:
                                    return '4px' // 增大到8px便于观察
                                  default:
                                    return '8px'
                                }
                              }

                              const borderRadius = getBorderRadius(row.depth)

                              // 计算位置
                              const leftOffset =
                                isFirstCell && row.depth > 0
                                  ? `${row.depth * 24 - 24}px`
                                  : '0'
                              const rightOffset =
                                isLastCell && row.depth > 0
                                  ? `${row.depth * 24 - 24}px`
                                  : '0'

                              return (
                                <>
                                  {/* 主边框容器 - 用于实现连续的圆角效果 */}
                                  <div
                                    className='absolute inset-0 pointer-events-none'
                                    style={{
                                      left: leftOffset,
                                      right: rightOffset,
                                      border: '1px solid #DFE3EA',
                                      borderTop: isFirstDataRow
                                        ? '1px solid #DFE3EA'
                                        : 'none',
                                      // 最后一行添加圆角
                                      ...(isLastRowInGroup && {
                                        borderBottomLeftRadius: isFirstCell
                                          ? borderRadius
                                          : '0',
                                        borderBottomRightRadius: isLastCell
                                          ? borderRadius
                                          : '0',
                                      }),
                                      // 只在边框位置显示
                                      borderLeft: isFirstCell
                                        ? '1px solid #DFE3EA'
                                        : 'none',
                                      borderRight: isLastCell
                                        ? '1px solid #DFE3EA'
                                        : 'none',
                                    }}
                                  />

                                  {/* 单元格间的垂直分隔线 */}
                                  {!isLastCell && (
                                    <div className='absolute top-0 bottom-0 right-0 w-px bg-[#DFE3EA] pointer-events-none' />
                                  )}
                                </>
                              )
                            })()}

                            {/* 在第一个单元格内添加左侧层级指示线 */}
                            {isFirstCell && row.depth > 0 && (
                              <div className='absolute left-0 top-0 bottom-0 flex items-stretch pointer-events-none'>
                                {Array.from({ length: row.depth - 1 }).map(
                                  (_, index) => (
                                    <div
                                      key={index}
                                      className='border-l border-solid border-[#DFE3EA]'
                                      style={{
                                        marginRight: '23px',
                                        height: '100%',
                                      }}
                                    />
                                  )
                                )}
                              </div>
                            )}

                            {/* 在最后一个单元格内添加右侧层级指示线 */}
                            {isLastCell && row.depth > 0 && (
                              <div className='absolute right-0 top-0 bottom-0 flex items-stretch pointer-events-none'>
                                {Array.from({ length: row.depth - 1 }).map(
                                  (_, index) => (
                                    <div
                                      key={index}
                                      className='border-r border-solid border-[#DFE3EA]'
                                      style={{
                                        marginLeft: '23px',
                                        height: '100%',
                                      }}
                                    />
                                  )
                                )}
                              </div>
                            )}

                            {/* 单元格内容 */}
                            <div
                              style={{
                                marginLeft:
                                  isFirstCell && row.depth > 0
                                    ? `${row.depth * 24}px`
                                    : '0',
                              }}
                            >
                              {flexRender(
                                cell.column.columnDef.cell,
                                cell.getContext()
                              )}
                            </div>
                          </td>
                        )
                      })}
                    </tr>

                    {/* 给小于等于2层级的最后一行数据行后添加tr */}
                    {(() => {
                      // 判断是否为分组内的最后一行数据
                      const isLastDataRowInGroup = (() => {
                        // 如果是整个表格的最后一行，肯定是最后一行
                        if (rowIndex === rows.length - 1) return true

                        // 检查下一行
                        const nextRow = rows[rowIndex + 1]
                        if (!nextRow) return true

                        // 如果当前行不是分组行，且下一行是分组行，则当前行是分组的最后一行
                        if (!row.getIsGrouped() && nextRow.getIsGrouped()) return true

                        return false
                      })()

                      return row.depth <= 2 && isLastDataRowInGroup && (
                        <tr>
                          <td
                            colSpan={table.getAllLeafColumns().length}
                            className={`p-0 ${
                              row.depth === 2 && isParentLastInGrandParent(row, rowIndex) ? 'pb-2' : ''
                            }`}
                          >
                            {(() => {
                              // 根据层级和父分组状态决定cyan区域的样式
                              if (row.depth === 1) {
                                // 一层分组：没有边框，只有占位
                                return <div className='h-2' />
                              } else if (row.depth === 2) {
                                // 二层分组：检查父亲是否是分组的最后一个
                                const parentIsLast = isParentLastInGrandParent(row, rowIndex)
                                if (parentIsLast) {
                                  // 父亲是最后一个：添加底边框和左右边框
                                  return <div className='h-2 border-l border-r border-b border-solid border-[#DFE3EA] rounded-br-[8px] rounded-bl-[8px]' />
                                } else {
                                  // 父亲不是最后一个：只有左右边框
                                  return <div className='h-2 border-l border-r border-solid border-[#DFE3EA]' />
                                }
                              }
                            })()}
                          </td>
                        </tr>
                      )
                    })()}

                    {/* 在数据行结束后检查是否需要添加分组结束的绿色tr */}
                    {(() => {
                      // 检查当前数据行是否是某个分组的最后一个元素
                      const groupsToEnd = []
                      for (const [groupId, endPosition] of groupEndPositions) {
                        if (endPosition === rowIndex) {
                          // 找到对应的分组行
                          const groupRow = rows.find((r) => r.id === groupId)
                          if (groupRow && groupRow.depth > 1) {
                            // 检查父层级是否为最后一个
                            const parentIsLast = isParentLastInGrandParent(
                              groupRow,
                              rowIndex
                            )
                            console.log(
                              `分组 ${groupRow.groupingValue} 的父层级是否为最后一个: ${parentIsLast}`
                            )

                            groupsToEnd.push({ groupRow, parentIsLast })
                          }
                        }
                      }

                      //  包裹二
                      // 渲染底部占位边框
                      return groupsToEnd.map((item, _gIndex) => (
                        <>
                          <tr key={`group-end-${item.groupRow.id}`}>
                            <td
                              colSpan={table.getAllLeafColumns().length}
                              className={`p-0 border-solid border-[#DFE3EA] ${
                                row.depth < 0
                                  ? 'border-l border-r'
                                  : 'border-l border-r'
                              }`}
                            >
                              <div className='flex w-full'>
                                {Array.from({
                                  length: row.depth - _gIndex - 2,
                                }).map((_, boxIndex) => (
                                  <div
                                    key={boxIndex}
                                    className={`${
                                      boxIndex < 1 ? '' : 'border-l'
                                    } border-solid border-[#DFE3EA] w-[23px] h-2`}
                                  />
                                ))}
                                <div
                                  className={`flex-1 p-0 border-l border-r border-b border-solid border-[#DFE3EA] ${
                                    row.depth < 0 ? 'mx-6' : ''
                                  }`}
                                  style={{
                                    height: `8px`,
                                    borderRadius: `0 0 ${6 + row.depth}px ${
                                      6 + row.depth
                                    }px`,
                                  }}
                                ></div>
                                {Array.from({
                                  length: row.depth - _gIndex - 2,
                                }).map((_, boxIndex) => (
                                  <div
                                    key={boxIndex}
                                    className={`${
                                      boxIndex < 1 ? '' : 'border-r'
                                    } border-solid border-[#DFE3EA] w-[23px] h-2`}
                                  />
                                ))}
                              </div>
                            </td>
                          </tr>
                          <tr className='h-2'>
                            {(() => {
                              const endingGroup = item.groupRow
                              const parentIsLast = isParentLastInGrandParent(
                                endingGroup,
                                rowIndex
                              )
                              return (
                                <td className={`h-full p-0 ${parentIsLast ? 'pb-2' : ''}`} colSpan={5}>
                                  <div
                                    className={`border-solid border-[#DFE3EA] border-l border-r w-full h-[8px] ${parentIsLast ? 'border-b' : ''}`}
                                    style={{
                                      borderRadius: parentIsLast ? `0 0 ${6 + row.depth}px ${
                                        6 + row.depth
                                      }px`: 'none',
                                    }}
                                    />
                                  
                                </td>
                              )
                            })()}
                          </tr>
                        </>
                      ))
                    })()}
                  </>
                )
              })
            })()}
          </tbody>
        </table>
      </div>
    </div>
  )
}

export default GroupableTaskTable
