import { createColumnHelper } from '@tanstack/react-table'
import { TaskData } from '../interface'
import {
  renderTaskNameCell,
  renderStatusCell,
  renderAssigneeCell,
  renderPriorityCell,
} from './cell-renderers'

// ==================== 业务列配置 ====================
const columnHelper = createColumnHelper<TaskData>()

export const columns = [
  columnHelper.accessor('taskName', {
    header: '任务名称',
    cell: renderTaskNameCell,
  }),
  columnHelper.accessor('status', {
    header: '状态',
    cell: renderStatusCell,
  }),
  columnHelper.accessor('assignee', {
    header: '负责人',
    cell: renderAssigneeCell,
  }),
  columnHelper.accessor('priority', {
    header: '优先级',
    cell: renderPriorityCell,
  }),
  columnHelper.accessor('team', { 
    header: '团队' 
  }),
]
