import {
  Column,
  flexRender,
  <PERSON><PERSON>,
  HeaderGroup,
  Table,
} from '@tanstack/react-table'
import { Virtualizer } from '@tanstack/react-virtual'
import { getCommonPinningStyles } from '../utils/style'
import classNames from 'classnames'

interface TableHeadProps {
  columnVirtualizer: Virtualizer<HTMLDivElement, HTMLTableCellElement>
  table: Table<any>
  virtualPaddingLeft: number | undefined
  virtualPaddingRight: number | undefined
  pinned?: [number, number?]
}

export function TableHead({
  columnVirtualizer,
  table,
  virtualPaddingLeft,
  virtualPaddingRight,
  pinned,
}: TableHeadProps) {
  return (
    <thead
      style={{
        display: 'grid',
        position: 'sticky',
        top: 0,
        zIndex: 1,
      }}
    >
      {table.getHeaderGroups().map((headerGroup) => (
        <TableHeadRow
          columnVirtualizer={columnVirtualizer}
          headerGroup={headerGroup}
          key={headerGroup.id}
          virtualPaddingLeft={virtualPaddingLeft}
          virtualPaddingRight={virtualPaddingRight}
          pinned={pinned}
        />
      ))}
    </thead>
  )
}

interface TableHeadRowProps {
  columnVirtualizer: Virtualizer<HTMLDivElement, HTMLTableCellElement>
  headerGroup: HeaderGroup<any>
  virtualPaddingLeft: number | undefined
  virtualPaddingRight: number | undefined
  pinned?: [number, number?]
}

function TableHeadRow({
  columnVirtualizer,
  headerGroup,
  virtualPaddingLeft,
  virtualPaddingRight,
  pinned,
}: TableHeadRowProps) {
  let virtualColumns = columnVirtualizer.getVirtualItems()

  if (pinned?.[0]) {
    virtualColumns = virtualColumns.slice(pinned[0])
  }
  if (pinned?.[1]) {
    virtualColumns = virtualColumns.slice(0, -pinned[1])
  }

  const pinLeftHeader = pinned?.[0]
    ? headerGroup.headers.slice(0, pinned[0])
    : []

  const pinRightHeader = pinned?.[1]
    ? headerGroup.headers.slice(-pinned[1])
    : []

  return (
    <tr key={headerGroup.id} style={{ display: 'flex', width: '100%' }}>
      {pinLeftHeader.map((header, index) => {
        const left = pinLeftHeader
          .slice(0, index)
          .reduce((total, column) => total + column.getSize(), 0)
        return (
          <TableHeadCell
            key={header.id}
            header={header}
            pin='left'
            pinWidth={left}
          />
        )
      })}
      {virtualPaddingLeft ? (
        //fake empty column to the left for virtualization scroll padding
        <th style={{ display: 'flex', width: virtualPaddingLeft }} />
      ) : null}
      {virtualColumns.map((virtualColumn) => {
        const header = headerGroup.headers[virtualColumn.index]
        return <TableHeadCell key={header.id} header={header} />
      })}
      {virtualPaddingRight ? (
        //fake empty column to the right for virtualization scroll padding
        <th style={{ display: 'flex', width: virtualPaddingRight }} />
      ) : null}
      {pinRightHeader.map((header, index) => {
        const right = pinRightHeader
          .slice(index + 1)
          .reduce((total, column) => total + column.getSize(), 0)
        return (
          <TableHeadCell
            key={header.id}
            header={header}
            pin='right'
            pinWidth={right}
          />
        )
      })}
    </tr>
  )
}

interface TableHeadCellProps {
  header: Header<any, unknown>
  pin?: 'left' | 'right'
  pinWidth?: number
}

function TableHeadCell({ header, pin, pinWidth }: TableHeadCellProps) {
  return (
    <th
      key={header.id}
      style={{
        display: 'flex',
        // width: header.getSize(),
        ...getCommonPinningStyles(header.column, pin, pinWidth),
      }}
    >
      <div
        className={classNames('w-full', {
          'cursor-pointer select-none': header.column.getCanSort(),
        })}
        onClick={header.column.getToggleSortingHandler()}
      >
        {flexRender(header.column.columnDef.header, header.getContext())}
        {/* <span onClick={() => header.column.pin('left')}>
          {header.column.getIsPinned() ? '📌' : 'x'}
        </span> */}
        {/* {{
          asc: ' 🔼',
          desc: ' 🔽',
        }[header.column.getIsSorted() as string] ?? null} */}
      </div>
    </th>
  )
}
