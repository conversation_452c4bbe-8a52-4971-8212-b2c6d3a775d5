import { Cell, Column, flexRender, Row, Table } from '@tanstack/react-table'
import {
  useVirtualizer,
  VirtualItem,
  Virtualizer,
} from '@tanstack/react-virtual'
import React from 'react'
import { getCommonPinningStyles } from '../utils/style'

interface TableBodyProps {
  columnVirtualizer: Virtualizer<HTMLDivElement, HTMLTableCellElement>
  table: Table<any>
  tableContainerRef: React.RefObject<HTMLDivElement>
  virtualPaddingLeft: number | undefined
  virtualPaddingRight: number | undefined
  pinned?: [number, number?]
}

export function TableBody({
  columnVirtualizer,
  table,
  tableContainerRef,
  virtualPaddingLeft,
  virtualPaddingRight,
  pinned,
}: TableBodyProps) {
  const { rows } = table.getRowModel()

  //dynamic row height virtualization - alternatively you could use a simpler fixed row height strategy without the need for `measureElement`
  const rowVirtualizer = useVirtualizer<HTMLDivElement, HTMLTableRowElement>({
    count: rows.length,
    estimateSize: () => 33, //estimate row height for accurate scrollbar dragging
    getScrollElement: () => tableContainerRef.current,
    //measure dynamic row height, except in firefox because it measures table border height incorrectly
    measureElement:
      typeof window !== 'undefined' &&
      navigator.userAgent.indexOf('Firefox') === -1
        ? (element) => element?.getBoundingClientRect().height
        : undefined,
    overscan: 5,
  })

  const virtualRows = rowVirtualizer.getVirtualItems()

  return (
    <tbody
      style={{
        display: 'grid',
        height: `${rowVirtualizer.getTotalSize()}px`, //tells scrollbar how big the table is
        position: 'relative', //needed for absolute positioning of rows
      }}
    >
      {virtualRows.map((virtualRow) => {
        const row = rows[virtualRow.index] as Row<any>

        return (
          <TableBodyRow
            columnVirtualizer={columnVirtualizer}
            key={row.id}
            row={row}
            rowVirtualizer={rowVirtualizer}
            virtualPaddingLeft={virtualPaddingLeft}
            virtualPaddingRight={virtualPaddingRight}
            virtualRow={virtualRow}
            pinned={pinned}
          />
        )
      })}
    </tbody>
  )
}

interface TableBodyRowProps {
  columnVirtualizer: Virtualizer<HTMLDivElement, HTMLTableCellElement>
  row: Row<any>
  rowVirtualizer: Virtualizer<HTMLDivElement, HTMLTableRowElement>
  virtualPaddingLeft: number | undefined
  virtualPaddingRight: number | undefined
  virtualRow: VirtualItem
  pinned?: [number, number?]
}

function TableBodyRow({
  columnVirtualizer,
  row,
  rowVirtualizer,
  virtualPaddingLeft,
  virtualPaddingRight,
  virtualRow,
  pinned,
}: TableBodyRowProps) {
  const visibleCells = row.getVisibleCells()
  let virtualColumns = columnVirtualizer.getVirtualItems()

  if (pinned?.[0]) {
    virtualColumns = virtualColumns.slice(pinned[0])
  }
  if (pinned?.[1]) {
    virtualColumns = virtualColumns.slice(0, -pinned[1])
  }

  const pinLeftCells = pinned?.[0] ? visibleCells.slice(0, pinned[0]) : []
  const pinRightCells = pinned?.[1] ? visibleCells.slice(-pinned[1]) : []

  return (
    <tr
      data-index={virtualRow.index} //needed for dynamic row height measurement
      ref={(node) => rowVirtualizer.measureElement(node)} //measure dynamic row height
      key={row.id}
      style={{
        display: 'flex',
        position: 'absolute',
        transform: `translateY(${virtualRow.start}px)`, //this should always be a `style` as it changes on scroll
        width: '100%',
      }}
    >
      {pinLeftCells.map((cell, index) => {
        const left = pinLeftCells
          .slice(0, index)
          .reduce((total, cell) => total + cell.column.getSize(), 0)
        return (
          <TableBodyCell key={cell.id} cell={cell} pin='left' pinWidth={left} />
        )
      })}
      {virtualPaddingLeft ? (
        //fake empty column to the left for virtualization scroll padding
        <td style={{ display: 'flex', width: virtualPaddingLeft }} />
      ) : null}
      {virtualColumns.map((vc) => {
        const cell = visibleCells[vc.index]
        return <TableBodyCell key={cell.id} cell={cell} />
      })}
      {virtualPaddingRight ? (
        //fake empty column to the right for virtualization scroll padding
        <td style={{ display: 'flex', width: virtualPaddingRight }} />
      ) : null}
      {pinRightCells.map((cell, index) => {
        const right = pinRightCells
          .slice(0, index)
          .reduce((total, cell) => total + cell.column.getSize(), 0)
        return (
          <TableBodyCell
            key={cell.id}
            cell={cell}
            pin='right'
            pinWidth={right}
          />
        )
      })}
    </tr>
  )
}

interface TableBodyCellProps {
  cell: Cell<any, unknown>
  pin?: 'left' | 'right'
  pinWidth?: number
}

function TableBodyCell({ cell, pin, pinWidth }: TableBodyCellProps) {
  return (
    <td
      key={cell.id}
      style={{
        display: 'flex',
        // width: cell.column.getSize(),
        ...getCommonPinningStyles(cell.column, pin, pinWidth),
      }}
    >
      {flexRender(cell.column.columnDef.cell, cell.getContext())}
    </td>
  )
}
