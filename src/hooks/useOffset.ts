import { useSize } from "ahooks"
import debounce from "lodash/debounce"
import { useEffect, useState } from "react"

export const useOffset = (target: HTMLElement, scrollContainer?: HTMLElement) => {
  
  const [moving, setMoving] = useState(false)
  const [offset, setOffset] = useState({ x: 0, y: 0, moveDiraction: '' })
  const targetSize = useSize(target)
  const scrollContainerSize = useSize(scrollContainer)
  useEffect(() => {
    if (!target || !scrollContainer) return

    let timer: number | null = null
    const updateOffset = debounce(function updateOffset() {
      if (!scrollContainer) return
      const targetRect = target.getBoundingClientRect()
      const scrollContainerRect = scrollContainer.getBoundingClientRect()
  
      // 计算 target 的底部是否在可视区域内
      const targetBottom = targetRect.y + targetRect.height;
      const targetRight = targetRect.x + targetRect.width;
      const scrollContainerBottom = scrollContainerRect.y + scrollContainerRect.height;
      const scrollContainerRight = scrollContainerRect.x + scrollContainerRect.width;

      const xIsOut = targetRect.left > scrollContainerRight
      const targetOffsetX = scrollContainerRight - targetRight;
  
      const yIsOut = targetRect.top > scrollContainerBottom
      const targetOffsetY = scrollContainerBottom - targetBottom;
      
      setOffset((value) => {
        let newValue = {
          x: (xIsOut || targetOffsetX > 0) ? 0 : targetOffsetX,
          y: (yIsOut || targetOffsetY > 0) ? 0 : targetOffsetY,
          moveDiraction: ''
        }
        if (newValue.x !== value.x) {
          newValue.moveDiraction = 'x'
        }
        if (newValue.y !== value.y) {
          newValue.moveDiraction = 'y'
        }
        return newValue
      })
      setMoving(false)
    }, 100)

    const onScrollHandler = () => {
      setMoving(true)
      updateOffset()
    }

    updateOffset()
    scrollContainer.addEventListener('scroll', onScrollHandler)
    window.addEventListener('resize', onScrollHandler)
    return () => {
      scrollContainer.removeEventListener('scroll', onScrollHandler)
      window.removeEventListener('resize', onScrollHandler)
    }
  }, [target, scrollContainer, targetSize?.width, targetSize?.height, scrollContainerSize?.width, scrollContainerSize?.height])

  return { ...offset, moving}
}