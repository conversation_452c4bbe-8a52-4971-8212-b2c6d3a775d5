import { useUpdateEffect } from 'ahooks'
import { useCallback, useRef, useState } from 'react'

export interface FormItemProps<Value> {
  value?: Value
  defaultValue?: Value
  onChange?: (value: Value, ...args: any[]) => void
  disabled?: boolean
}

export const useFormItemValue = <Value>(info: FormItemProps<Value>) => {
  const { value, defaultValue, onChange, disabled } = info
  const [formValue, setFormValue] = useState<Value | undefined>(
    value || defaultValue
  )
  useUpdateEffect(() => {
    setFormValue(value)
  }, [value])

  const valueRef = useRef(formValue)
  valueRef.current = formValue

  const setValue = useCallback(
    (newValue: Value, ...other: any[]) => {
      if (disabled) return

      setFormValue(newValue)
      onChange?.(newValue, other)
    },
    [onChange, disabled]
  )

  return {
    value: formValue,
    ref: valueRef,
    set: setValue,
  }
}
