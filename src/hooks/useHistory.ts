import { useRefState } from "@tita/hooks"
import { useCallback, useState } from "react"

type History = { key: string | number, props?: Record<string, any> }
export const useHistory = (defaultHistory: History[]) => {
  const [history, historyRef, setHistory] = useRefState(defaultHistory || [])

  const push = useCallback((key: string | number, props?: Record<string, any>) => {
    setHistory([...historyRef.current, { key, props }])
  }, [])

  const pop = useCallback(() => {
    setHistory(historyRef.current.slice(0, -1))
  }, [])

  const get = useCallback(() => {
    return historyRef.current
  }, [])

  const set = useCallback((history: History[]) => {
    return setHistory(history)
  }, [])

  const clear = useCallback(() => {
    setHistory([])
  }, [])

  return {
    current: history[history.length - 1],
    back: history[history.length - 2],
    push,
    pop,
    get,
    set,
    clear
  }
}