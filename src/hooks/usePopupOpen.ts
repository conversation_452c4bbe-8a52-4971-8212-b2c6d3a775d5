import { useCallback, useState } from 'react'

export const usePopupOpen = (props?: {
  defaultOpen?: boolean
  onClose?: () => void
}) => {
  const { defaultOpen, onClose } = props || {}
  const [open, setOpen] = useState(!!defaultOpen)
  const show = useCallback(() => {
    setOpen(true)
  }, [onClose])

  const hide = useCallback(() => {
    setOpen(false)
    onClose?.()
  }, [onClose])

  const toggle = useCallback(() => {
    setOpen((prev) => !prev)
    if (!open) {
      onClose?.()
    }
  }, [open, onClose])

  return {
    open,
    set: setOpen,
    show,
    hide,
    toggle,
  }
}
