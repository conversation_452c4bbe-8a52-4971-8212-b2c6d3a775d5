import { createUseObserver } from '@tita/hooks';
import { useUpdateEffect } from 'ahooks';
import { useMemo, useState } from 'react';

const useLocalObserver = createUseObserver<{
  [key: string]: any;
}>();

const createUpdateFun = <T>(value: T) => {
  if (typeof value === 'object') {
    return {
      get: (key: string) => {
        const cacheValue = window.localStorage.getItem(key);
        try {
          if (cacheValue) return JSON.parse(cacheValue) as T;
          else return undefined;
        } catch (error) {
          return undefined
        }
      },
      set: (key: string, value: T) =>
        window.localStorage.setItem(key, JSON.stringify(value)),
    };
  } else {
    return {
      get: (key: string) => window.localStorage.getItem(key) as unknown as T,
      set: (key: string, value: T) =>
        window.localStorage.setItem(key, value as unknown as string),
    };
  }
};
export const useLocalStorage = <T>(
  key: string | undefined,
  initialValue: T,
) => {
  const { get, set } = useMemo(
    () => createUpdateFun(initialValue),
    [initialValue],
  );
  // 如果没有缓存,需要更新外部传来的 initialValue
  const [hasCache, setHasCacheData] = useState(false);
  const [value, setValue] = useState<T>(() => {
    if (!key) return initialValue;
    const cacheValue = get(key);
    setHasCacheData(cacheValue !== undefined);
    return cacheValue !== undefined ? cacheValue : initialValue;
  });

  useUpdateEffect(() => {
    if (!hasCache) setValue(initialValue);
  }, [initialValue]);

  const { emit } = useLocalObserver(`${key}_update`, (value: T) => {
    setValue(value);
    setHasCacheData(true);
  });

  return [
    value,
    (value: T) => {
      if (key) set(key, value);
      setHasCacheData(true);
      setValue(value);
      emit(value);
    },
  ] as const;
};
