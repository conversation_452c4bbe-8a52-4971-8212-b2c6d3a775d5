import { useRefState } from '@tita/hooks'
import { useState, useEffect, useRef, useCallback } from 'react'

const getPostion = (
  e:
    | React.TouchEvent<HTMLDivElement>
    | React.MouseEvent<HTMLDivElement, MouseEvent>
) => {
  // @ts-ignore
  if (e?.pageX) {
    // @ts-ignore
    const { pageX, pageY } = e
    return { x: pageX, y: pageY }
  }
  // @ts-ignore
  if (e?.nativeEvent?.pageX) {
    // @ts-ignore
    const { pageX, pageY } = e.nativeEvent
    return { x: pageX, y: pageY }
  }
  // @ts-ignore
  if (e?.nativeEvent?.changedTouches) {
    // @ts-ignore
    const { pageX, pageY } = e.nativeEvent.changedTouches[0]
    return { x: pageX, y: pageY }
  }
  return { x: 0, y: 0 }
}

export type UseTouchEvent = {
  event:
    | React.TouchEvent<HTMLDivElement>
    | React.MouseEvent<HTMLDivElement, MouseEvent>
  first: boolean
  down: boolean
  movement: number[]
  lastOffset: number[]
  cancel: () => void
}

export type UseTouchBind = {
  onTouchStart: React.TouchEventHandler<HTMLDivElement>
  onMouseDown: React.MouseEventHandler<HTMLDivElement>
  onTouchMove: React.TouchEventHandler<HTMLDivElement>
  onMouseMove: React.MouseEventHandler<HTMLDivElement>
  onTouchEnd: React.TouchEventHandler<HTMLDivElement>
  onMouseUp: React.MouseEventHandler<HTMLDivElement>
}

export const useTouch = (callback: (event: UseTouchEvent) => void) => {
  const callbackRef = useRef(callback)
  callbackRef.current = callback

  const [down, downRef, setDown] = useRefState(false)
  const [isCancel, isCancelRef, setIsCancel] = useRefState(false)
  const [lastOffset, lastOffsetRef, setLastOffset] = useRefState<number[]>([
    0, 0,
  ])
  const [startPostion, startPostionRef, setStartPostion] = useRefState<{
    x: number
    y: number
  }>({
    x: 0,
    y: 0,
  })

  const cancel = () => {
    setDown(false)
    setIsCancel(true)
  }

  const onTouchMove:
    | React.TouchEventHandler<HTMLDivElement>
    | React.MouseEventHandler<HTMLDivElement> = useCallback((e) => {

    if (!downRef.current || isCancelRef.current) return
    const { x, y } = getPostion(e)

    callbackRef.current({
      event: e,
      first: false,
      down: true,
      movement: [x - startPostionRef.current.x, y - startPostionRef.current.y],
      cancel,
      lastOffset: lastOffsetRef.current,
    })
  }, [])

  const onTouchEnd:
    | React.TouchEventHandler<HTMLDivElement>
    | React.MouseEventHandler<HTMLDivElement> = useCallback((e) => {

    const { x, y } = getPostion(e)
    setLastOffset([
      lastOffsetRef.current[0] + x - startPostionRef.current.x,
      lastOffsetRef.current[1] + y - startPostionRef.current.y,
    ])
    if (downRef.current)
      callbackRef.current({
        event: e,
        first: false,
        down: false,
        movement: [
          x - startPostionRef.current.x,
          y - startPostionRef.current.y,
        ],
        cancel,
        lastOffset: lastOffsetRef.current,
      })
    setDown(false)
    setIsCancel(false)

    document.removeEventListener('mousemove', onTouchMove)
    document.removeEventListener('touchmove', onTouchMove)
    document.removeEventListener('mouseup', onTouchEnd)
    document.removeEventListener('touchend', onTouchEnd)
  }, [])

  const onTouchStart:
    | React.TouchEventHandler<HTMLDivElement>
    | React.MouseEventHandler<HTMLDivElement> = useCallback((e) => {

    const { x, y } = getPostion(e)
    setStartPostion({ x, y })
    setDown(true)
    callbackRef.current({
      event: e,
      first: true,
      down: true,
      movement: [0, 0],
      cancel,
      lastOffset: lastOffsetRef.current,
    })

    document.addEventListener('mousemove', onTouchMove)
    document.addEventListener('touchmove', onTouchMove)
    document.addEventListener('mouseup', onTouchEnd)
    document.addEventListener('touchend', onTouchEnd)
  }, [])

  return {
    onTouchStart,
    onMouseDown: onTouchStart,
  } as UseTouchBind
}
