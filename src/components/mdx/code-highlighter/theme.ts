export const theme = {
  name: 'github-dark',
  colors: {
    focusBorder: '#1f6feb',
    foreground: '#c9d1d9',
    descriptionForeground: '#8b949e',
    errorForeground: '#f85149',
    'textLink.foreground': '#58a6ff',
    'textLink.activeForeground': '#58a6ff',
    'textBlockQuote.background': '#010409',
    'textBlockQuote.border': '#30363d',
    'textCodeBlock.background': '#6e768166',
    'textPreformat.foreground': '#8b949e',
    'textSeparator.foreground': '#21262d',
    'button.background': '#238636',
    'button.foreground': '#ffffff',
    'button.hoverBackground': '#2ea043',
    'button.secondaryBackground': '#282e33',
    'button.secondaryForeground': '#c9d1d9',
    'button.secondaryHoverBackground': '#30363d',
    'checkbox.background': '#161b22',
    'checkbox.border': '#30363d',
    'dropdown.background': '#161b22',
    'dropdown.border': '#30363d',
    'dropdown.foreground': '#c9d1d9',
    'dropdown.listBackground': '#161b22',
    'input.background': '#0d1117',
    'input.border': '#30363d',
    'input.foreground': '#c9d1d9',
    'input.placeholderForeground': '#484f58',
    'badge.foreground': '#f0f6fc',
    'badge.background': '#1f6feb',
    'progressBar.background': '#1f6feb',
    'titleBar.activeForeground': '#8b949e',
    'titleBar.activeBackground': '#0d1117',
    'titleBar.inactiveForeground': '#8b949e',
    'titleBar.inactiveBackground': '#010409',
    'titleBar.border': '#30363d',
    'activityBar.foreground': '#c9d1d9',
    'activityBar.inactiveForeground': '#8b949e',
    'activityBar.background': '#0d1117',
    'activityBarBadge.foreground': '#f0f6fc',
    'activityBarBadge.background': '#1f6feb',
    'activityBar.activeBorder': '#f78166',
    'activityBar.border': '#30363d',
    'sideBar.foreground': '#c9d1d9',
    'sideBar.background': '#010409',
    'sideBar.border': '#30363d',
    'sideBarTitle.foreground': '#c9d1d9',
    'sideBarSectionHeader.foreground': '#c9d1d9',
    'sideBarSectionHeader.background': '#010409',
    'sideBarSectionHeader.border': '#30363d',
    'list.hoverForeground': '#c9d1d9',
    'list.inactiveSelectionForeground': '#c9d1d9',
    'list.activeSelectionForeground': '#c9d1d9',
    'list.hoverBackground': '#6e76811a',
    'list.inactiveSelectionBackground': '#6e768166',
    'list.activeSelectionBackground': '#6e768166',
    'list.focusForeground': '#c9d1d9',
    'list.focusBackground': '#388bfd26',
    'list.inactiveFocusBackground': '#388bfd26',
    'list.highlightForeground': '#58a6ff',
    'tree.indentGuidesStroke': '#21262d',
    'notificationCenterHeader.foreground': '#8b949e',
    'notificationCenterHeader.background': '#161b22',
    'notifications.foreground': '#c9d1d9',
    'notifications.background': '#161b22',
    'notifications.border': '#30363d',
    'notificationsErrorIcon.foreground': '#f85149',
    'notificationsWarningIcon.foreground': '#d29922',
    'notificationsInfoIcon.foreground': '#58a6ff',
    'pickerGroup.border': '#30363d',
    'pickerGroup.foreground': '#8b949e',
    'quickInput.background': '#161b22',
    'quickInput.foreground': '#c9d1d9',
    'statusBar.foreground': '#8b949e',
    'statusBar.background': '#0d1117',
    'statusBar.border': '#30363d',
    'statusBar.noFolderBackground': '#0d1117',
    'statusBar.debuggingBackground': '#da3633',
    'statusBar.debuggingForeground': '#f0f6fc',
    'statusBarItem.prominentBackground': '#161b22',
    'editorGroupHeader.tabsBackground': '#010409',
    'editorGroupHeader.tabsBorder': '#30363d',
    'editorGroup.border': '#30363d',
    'tab.activeForeground': '#c9d1d9',
    'tab.inactiveForeground': '#8b949e',
    'tab.inactiveBackground': '#010409',
    'tab.activeBackground': '#0d1117',
    'tab.hoverBackground': '#0d1117',
    'tab.unfocusedHoverBackground': '#6e76811a',
    'tab.border': '#30363d',
    'tab.unfocusedActiveBorderTop': '#30363d',
    'tab.activeBorder': '#0d1117',
    'tab.unfocusedActiveBorder': '#0d1117',
    'tab.activeBorderTop': '#f78166',
    'breadcrumb.foreground': '#8b949e',
    'breadcrumb.focusForeground': '#c9d1d9',
    'breadcrumb.activeSelectionForeground': '#8b949e',
    'breadcrumbPicker.background': '#161b22',
    'editor.foreground': '#c9d1d9',
    'editor.background': '#0d1117',
    'editorWidget.background': '#161b22',
    'editor.foldBackground': '#6e76811a',
    'editor.lineHighlightBackground': '#6e76811a',
    'editorLineNumber.foreground': '#8b949e',
    'editorLineNumber.activeForeground': '#c9d1d9',
    'editorIndentGuide.background': '#21262d',
    'editorIndentGuide.activeBackground': '#30363d',
    'editorWhitespace.foreground': '#484f58',
    'editorCursor.foreground': '#58a6ff',
    'editor.findMatchBackground': '#9e6a03',
    'editor.findMatchHighlightBackground': '#f2cc6080',
    'editor.linkedEditingBackground': '#58a6ff12',
    'editor.inactiveSelectionBackground': '#58a6ff12',
    'editor.selectionBackground': '#58a6ff33',
    'editor.selectionHighlightBackground': '#3fb95040',
    'editor.wordHighlightBackground': '#6e768180',
    'editor.wordHighlightBorder': '#6e768199',
    'editor.wordHighlightStrongBackground': '#6e76814d',
    'editor.wordHighlightStrongBorder': '#6e768199',
    'editorBracketMatch.background': '#3fb95040',
    'editorBracketMatch.border': '#3fb95099',
    'editorGutter.modifiedBackground': '#bb800966',
    'editorGutter.addedBackground': '#2ea04366',
    'editorGutter.deletedBackground': '#f8514966',
    'diffEditor.insertedTextBackground': '#2ea04326',
    'diffEditor.removedTextBackground': '#f8514926',
    'scrollbar.shadow': '#484f5833',
    'scrollbarSlider.background': '#6e768133',
    'scrollbarSlider.hoverBackground': '#6e768145',
    'scrollbarSlider.activeBackground': '#6e768187',
    'editorOverviewRuler.border': '#010409',
    'panel.background': '#010409',
    'panel.border': '#30363d',
    'panelTitle.activeBorder': '#f78166',
    'panelTitle.activeForeground': '#c9d1d9',
    'panelTitle.inactiveForeground': '#8b949e',
    'panelInput.border': '#30363d',
    'terminal.foreground': '#8b949e',
    'terminal.ansiBlack': '#484f58',
    'terminal.ansiRed': '#ff7b72',
    'terminal.ansiGreen': '#3fb950',
    'terminal.ansiYellow': '#d29922',
    'terminal.ansiBlue': '#58a6ff',
    'terminal.ansiMagenta': '#bc8cff',
    'terminal.ansiCyan': '#39c5cf',
    'terminal.ansiWhite': '#b1bac4',
    'terminal.ansiBrightBlack': '#6e7681',
    'terminal.ansiBrightRed': '#ffa198',
    'terminal.ansiBrightGreen': '#56d364',
    'terminal.ansiBrightYellow': '#e3b341',
    'terminal.ansiBrightBlue': '#79c0ff',
    'terminal.ansiBrightMagenta': '#d2a8ff',
    'terminal.ansiBrightCyan': '#56d4dd',
    'terminal.ansiBrightWhite': '#f0f6fc',
    'gitDecoration.addedResourceForeground': '#3fb950',
    'gitDecoration.modifiedResourceForeground': '#d29922',
    'gitDecoration.deletedResourceForeground': '#f85149',
    'gitDecoration.untrackedResourceForeground': '#3fb950',
    'gitDecoration.ignoredResourceForeground': '#484f58',
    'gitDecoration.conflictingResourceForeground': '#db6d28',
    'gitDecoration.submoduleResourceForeground': '#8b949e',
    'debugToolBar.background': '#161b22',
    'editor.stackFrameHighlightBackground': '#bb800966',
    'editor.focusedStackFrameHighlightBackground': '#2ea04366',
    'peekViewEditor.matchHighlightBackground': '#bb800966',
    'peekViewResult.matchHighlightBackground': '#bb800966',
    'peekViewEditor.background': '#6e76811a',
    'peekViewResult.background': '#0d1117',
    'settings.headerForeground': '#8b949e',
    'settings.modifiedItemIndicator': '#bb800966',
    'welcomePage.buttonBackground': '#21262d',
    'welcomePage.buttonHoverBackground': '#30363d',
  },
  semanticHighlighting: true,
  tokenColors: [
    {
      scope: ['comment', 'punctuation.definition.comment', 'string.comment'],
      settings: {
        foreground: '#8b949e',
      },
    },
    {
      scope: [
        'constant',
        'entity.name.constant',
        'variable.other.constant',
        'variable.language',
        'entity',
      ],
      settings: {
        foreground: '#79c0ff',
      },
    },
    {
      scope: ['entity.name', 'meta.export.default', 'meta.definition.variable'],
      settings: {
        foreground: '#ffa657',
      },
    },
    {
      scope: [
        'variable.parameter.function',
        'meta.jsx.children',
        'meta.block',
        'meta.tag.attributes',
        'entity.name.constant',
        'meta.object.member',
        'meta.embedded.expression',
      ],
      settings: {
        foreground: '#c9d1d9',
      },
    },
    {
      scope: 'entity.name.function',
      settings: {
        foreground: '#d2a8ff',
      },
    },
    {
      scope: ['entity.name.tag', 'support.class.component'],
      settings: {
        foreground: '#7ee787',
      },
    },
    {
      scope: 'keyword',
      settings: {
        foreground: '#ff7b72',
      },
    },
    {
      scope: ['storage', 'storage.type'],
      settings: {
        foreground: '#ff7b72',
      },
    },
    {
      scope: [
        'storage.modifier.package',
        'storage.modifier.import',
        'storage.type.java',
      ],
      settings: {
        foreground: '#c9d1d9',
      },
    },
    {
      scope: [
        'string',
        'punctuation.definition.string',
        'string punctuation.section.embedded source',
      ],
      settings: {
        foreground: '#a5d6ff',
      },
    },
    {
      scope: 'support',
      settings: {
        foreground: '#79c0ff',
      },
    },
    {
      scope: 'meta.property-name',
      settings: {
        foreground: '#79c0ff',
      },
    },
    {
      scope: 'variable',
      settings: {
        foreground: '#ffa657',
      },
    },
    {
      scope: 'variable.other',
      settings: {
        foreground: '#c9d1d9',
      },
    },
    {
      scope: 'invalid.broken',
      settings: {
        fontStyle: 'italic',
        foreground: '#ffa198',
      },
    },
    {
      scope: 'invalid.deprecated',
      settings: {
        fontStyle: 'italic',
        foreground: '#ffa198',
      },
    },
    {
      scope: 'invalid.illegal',
      settings: {
        fontStyle: 'italic',
        foreground: '#ffa198',
      },
    },
    {
      scope: 'invalid.unimplemented',
      settings: {
        fontStyle: 'italic',
        foreground: '#ffa198',
      },
    },
    {
      scope: 'carriage-return',
      settings: {
        fontStyle: 'italic underline',
        background: '#ff7b72',
        foreground: '#f0f6fc',
        content: '^M',
      },
    },
    {
      scope: 'message.error',
      settings: {
        foreground: '#ffa198',
      },
    },
    {
      scope: 'string source',
      settings: {
        foreground: '#c9d1d9',
      },
    },
    {
      scope: 'string variable',
      settings: {
        foreground: '#79c0ff',
      },
    },
    {
      scope: ['source.regexp', 'string.regexp'],
      settings: {
        foreground: '#a5d6ff',
      },
    },
    {
      scope: [
        'string.regexp.character-class',
        'string.regexp constant.character.escape',
        'string.regexp source.ruby.embedded',
        'string.regexp string.regexp.arbitrary-repitition',
      ],
      settings: {
        foreground: '#a5d6ff',
      },
    },
    {
      scope: 'string.regexp constant.character.escape',
      settings: {
        fontStyle: 'bold',
        foreground: '#7ee787',
      },
    },
    {
      scope: 'support.constant',
      settings: {
        foreground: '#79c0ff',
      },
    },
    {
      scope: 'support.variable',
      settings: {
        foreground: '#79c0ff',
      },
    },
    {
      scope: 'meta.module-reference',
      settings: {
        foreground: '#79c0ff',
      },
    },
    {
      scope: 'punctuation.definition.list.begin.markdown',
      settings: {
        foreground: '#ffa657',
      },
    },
    {
      scope: ['markup.heading', 'markup.heading entity.name'],
      settings: {
        fontStyle: 'bold',
        foreground: '#79c0ff',
      },
    },
    {
      scope: 'markup.quote',
      settings: {
        foreground: '#7ee787',
      },
    },
    {
      scope: 'markup.italic',
      settings: {
        fontStyle: 'italic',
        foreground: '#c9d1d9',
      },
    },
    {
      scope: 'markup.bold',
      settings: {
        fontStyle: 'bold',
        foreground: '#c9d1d9',
      },
    },
    {
      scope: 'markup.raw',
      settings: {
        foreground: '#79c0ff',
      },
    },
    {
      scope: [
        'markup.deleted',
        'meta.diff.header.from-file',
        'punctuation.definition.deleted',
      ],
      settings: {
        background: '#490202',
        foreground: '#ffa198',
      },
    },
    {
      scope: [
        'markup.inserted',
        'meta.diff.header.to-file',
        'punctuation.definition.inserted',
      ],
      settings: {
        background: '#04260f',
        foreground: '#7ee787',
      },
    },
    {
      scope: ['markup.changed', 'punctuation.definition.changed'],
      settings: {
        background: '#5a1e02',
        foreground: '#ffa657',
      },
    },
    {
      scope: ['markup.ignored', 'markup.untracked'],
      settings: {
        foreground: '#161b22',
        background: '#79c0ff',
      },
    },
    {
      scope: 'meta.diff.range',
      settings: {
        foreground: '#d2a8ff',
        fontStyle: 'bold',
      },
    },
    {
      scope: 'meta.diff.header',
      settings: {
        foreground: '#79c0ff',
      },
    },
    {
      scope: 'meta.separator',
      settings: {
        fontStyle: 'bold',
        foreground: '#79c0ff',
      },
    },
    {
      scope: 'meta.output',
      settings: {
        foreground: '#79c0ff',
      },
    },
    {
      scope: [
        'brackethighlighter.tag',
        'brackethighlighter.curly',
        'brackethighlighter.round',
        'brackethighlighter.square',
        'brackethighlighter.angle',
        'brackethighlighter.quote',
      ],
      settings: {
        foreground: '#8b949e',
      },
    },
    {
      scope: 'brackethighlighter.unmatched',
      settings: {
        foreground: '#ffa198',
      },
    },
    {
      scope: ['constant.other.reference.link', 'string.other.link'],
      settings: {
        foreground: '#a5d6ff',
        fontStyle: 'underline',
      },
    },
  ],
}