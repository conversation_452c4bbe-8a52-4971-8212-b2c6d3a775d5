.preview {
  border-radius: 10px;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  background-color: #fff;
}
.componentContainer {
  padding: 12px 16px;
}
.codeContainer {
  border-radius: 0 0 10px 10px;
  overflow: hidden;
}
:global .ch-code-scroll-content {
  position: relative !important;
}
:global .ant-tabs-top > .ant-tabs-nav {
  margin: 0;
  background-color: #fff;
}
/* .codeContainer :global .ch-codeblock {
  margin: 0;
}
.codeContainer :global .ch-codegroup {
  margin: 0;
}
.preview :global .ant-tabs-nav {
  margin: 0;
  background-color: #fff;
  border-top: 1px solid rgb(231, 231, 231);
}
.preview :global .ant-tabs-content-holder {
  background-color: #fff;
}
.preview :global .ant-tabs-content {
  padding: 5px;
}
.preview :global .ch-codegroup {
  margin: 0;
}
.preview :global .ant-tabs-tab .anticon {
  margin-right: 5px;
} */
