.propsEditor {
  overflow-x: auto;
}
.propsEditor > div {
  position: relative;
  width: max-content;
  min-width: 100%;
}
.propsEditor :global .ant-table-wrapper {
  max-width: auto !important;
}
.propsEditor
  :global
  .ant-table.ant-table-small
  .ant-table-tbody
  .ant-table-wrapper:only-child
  .ant-table {
  margin: 0;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
}
.propsEditor
  :global
  .ant-table-expanded-row.ant-table-expanded-row-level-1
  > .ant-table-cell {
  padding: 8px;
  /* box-shadow: inset 0 0 5px 0 rgba(0, 0, 0, .1); */
}
