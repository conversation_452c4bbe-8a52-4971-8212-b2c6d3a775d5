import { useEffect, useRef, useState } from 'react'
import ReactDOM from 'react-dom'

interface Props {
  selector?: string // 挂载组件节点
  className?: string // 挂载点class
  children: React.ReactNode
}

const appRoot = document.body

const Portals: React.FC<Props> = ({ selector, className, children }) => {
  const [container, setContainer] = useState<Element | null>(null)
  const elRef = useRef<HTMLDivElement | null>(null)

  useEffect(() => {
    const el = document.createElement('div')
    el.setAttribute('class', `document-mouse-event-ignore ${className || ''}`)
    elRef.current = el

    setTimeout(() => {
      if (selector) {
        const targetNode = document.querySelector(selector)
        if (targetNode) targetNode.appendChild(el)
      } else {
        appRoot.appendChild(el)
      }
      setContainer(el)
    }, 5)

    return () => {
      setTimeout(() => {
        if (selector) {
          const targetNode = document.querySelector(selector)
          if (targetNode) targetNode.removeChild(el)
        } else {
          appRoot.removeChild(el)
        }
      }, 5)
    }
  }, [selector, className])

  if (!container) return <></>

  return ReactDOM.createPortal(children, container)
}

export default Portals
