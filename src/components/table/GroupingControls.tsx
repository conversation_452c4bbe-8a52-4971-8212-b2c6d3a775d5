import React from 'react'
import { GroupingControlsProps } from './types'

// ==================== 分组控制组件（纯组件）====================
export const GroupingControls: React.FC<GroupingControlsProps & {
  getColumnHeader: (colId: string) => string
}> = ({
  grouping,
  groupableColumns,
  toggleGrouping,
  setGrouping,
  getColumnHeader,
}) => {
  return (
    <div className='mb-6'>
      <h2 className='text-lg font-semibold text-gray-900 mb-4'>任务列表</h2>

      <div className='flex flex-wrap gap-2 mb-4'>
        <span className='text-sm font-medium text-gray-700'>选择分组:</span>
        {groupableColumns.map((colId) => (
          <button
            key={colId}
            onClick={() => toggleGrouping(colId)}
            className={`px-3 py-1 text-sm rounded-md ${
              grouping.includes(colId)
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 text-gray-700'
            }`}
          >
            {getColumnHeader(colId)}
            {grouping.includes(colId) && ' ✓'}
          </button>
        ))}
      </div>

      {grouping.length > 0 && (
        <div className='flex flex-wrap gap-2 mb-4'>
          <span className='text-sm font-medium text-gray-700'>
            当前分组顺序:
          </span>
          {grouping.map((colId, index) => (
            <div
              key={colId}
              className='flex items-center bg-blue-100 text-blue-800 px-3 py-1 rounded-md text-sm'
            >
              <span>
                {index + 1}. {getColumnHeader(colId)}
              </span>
              <button
                onClick={() => toggleGrouping(colId)}
                className='ml-2 text-blue-600 hover:text-blue-800'
              >
                ×
              </button>
            </div>
          ))}
          <button
            onClick={() => setGrouping([])}
            className='px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded-md'
          >
            清除所有分组
          </button>
        </div>
      )}
    </div>
  )
}
