import React from 'react'
import { TableRowProps } from './types'
import { GroupRow } from './GroupRow'
import { DataRow } from './DataRow'

// ==================== 表格行组件 ====================
export const TableRow: React.FC<TableRowProps> = ({
  row,
  rowIndex,
  rows,
  table,
  isParentLastInGrandParent,
  groupEndPositions,
}) => {
  if (row.getIsGrouped()) {
    return (
      <GroupRow
        row={row}
        rowIndex={rowIndex}
        rows={rows}
        table={table}
        groupEndPositions={groupEndPositions}
      />
    )
  }

  return (
    <DataRow
      row={row}
      rowIndex={rowIndex}
      rows={rows}
      table={table}
      isParentLastInGrandParent={isParentLastInGrandParent}
      groupEndPositions={groupEndPositions}
    />
  )
}
