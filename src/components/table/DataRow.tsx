import React from 'react'
import { flexRender } from '@tanstack/react-table'
import { DataRowProps } from './types'
import { CellBorder } from './CellBorder'
import { LevelIndicators } from './LevelIndicators'
import { DataRowEndSpacing } from './DataRowEndSpacing'

// ==================== 数据行组件 ====================
export const DataRow: React.FC<DataRowProps> = ({
  row,
  rowIndex,
  rows,
  table,
  isParentLastInGrandParent,
  groupEndPositions,
}) => {
  // 判断是否为分组内的最后一行
  const isLastRowInGroup = (() => {
    if (rowIndex === rows.length - 1) return true
    const nextRow = rows[rowIndex + 1]
    if (!nextRow) return true
    if (!row.getIsGrouped() && nextRow.getIsGrouped()) return true
    return false
  })()

  return (
    <>
      <tr key={row.id} className='relative'>
        {row.getVisibleCells().map((cell: any, cellIndex: number) => {
          const isFirstCell = cellIndex === 0
          const isLastCell = cellIndex === row.getVisibleCells().length - 1

          return (
            <td
              key={cell.id}
              className='align-middle relative bg-white'
              style={{ position: 'relative' }}
            >
              {/* 单元格边框 */}
              <CellBorder
                row={row}
                rowIndex={rowIndex}
                rows={rows}
                isFirstCell={isFirstCell}
                isLastCell={isLastCell}
                isLastRowInGroup={isLastRowInGroup}
              />

              {/* 层级指示线 */}
              <LevelIndicators
                row={row}
                isFirstCell={isFirstCell}
                isLastCell={isLastCell}
              />

              {/* 单元格内容 */}
              <div
                style={{
                  marginLeft:
                    isFirstCell && row.depth > 0
                      ? `${row.depth * 24}px`
                      : '0',
                }}
              >
                {flexRender(
                  cell.column.columnDef.cell,
                  cell.getContext()
                )}
              </div>
            </td>
          )
        })}
      </tr>

      {/* 数据行结束后的占位行 */}
      <DataRowEndSpacing
        row={row}
        rowIndex={rowIndex}
        rows={rows}
        table={table}
        isParentLastInGrandParent={isParentLastInGrandParent}
        groupEndPositions={groupEndPositions}
      />
    </>
  )
}
