import React from 'react'
import { LevelIndicatorsProps } from './types'

// ==================== 层级指示线组件（纯组件）====================
export const LevelIndicators: React.FC<LevelIndicatorsProps> = ({
  row,
  isFirstCell,
  isLastCell,
}) => {
  return (
    <>
      {/* 左侧层级指示线 */}
      {isFirstCell && row.depth > 0 && (
        <div className='absolute left-0 top-0 bottom-0 flex items-stretch pointer-events-none'>
          {Array.from({ length: row.depth - 1 }).map((_, index) => (
            <div
              key={index}
              className='border-l border-solid border-[#DFE3EA]'
              style={{
                marginRight: '23px',
                height: '100%',
              }}
            />
          ))}
        </div>
      )}

      {/* 右侧层级指示线 */}
      {isLastCell && row.depth > 0 && (
        <div className='absolute right-0 top-0 bottom-0 flex items-stretch pointer-events-none'>
          {Array.from({ length: row.depth - 1 }).map((_, index) => (
            <div
              key={index}
              className='border-r border-solid border-[#DFE3EA]'
              style={{
                marginLeft: '23px',
                height: '100%',
              }}
            />
          ))}
        </div>
      )}
    </>
  )
}
