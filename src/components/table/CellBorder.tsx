import React from 'react'
import { CellBorderProps } from './types'
import { getBorderRadius } from './utils'

// ==================== 单元格边框组件 ====================
export const CellBorder: React.FC<CellBorderProps> = ({
  row,
  rowIndex,
  rows,
  isFirstCell,
  isLastCell,
  isLastRowInGroup,
}) => {
  const prevRow = rows[rowIndex - 1]
  const isFirstDataRow =
    !prevRow ||
    prevRow.getIsGrouped() ||
    prevRow.depth < row.depth

  const borderRadius = getBorderRadius(row.depth)
  const leftOffset =
    isFirstCell && row.depth > 0
      ? `${row.depth * 24 - 24}px`
      : '0'
  const rightOffset =
    isLastCell && row.depth > 0
      ? `${row.depth * 24 - 24}px`
      : '0'

  return (
    <>
      {/* 主边框容器 */}
      <div
        className='absolute inset-0 pointer-events-none'
        style={{
          left: leftOffset,
          right: rightOffset,
          border: '1px solid #DFE3EA',
          borderTop: isFirstDataRow
            ? '1px solid #DFE3EA'
            : 'none',
          ...(isLastRowInGroup && {
            borderBottomLeftRadius: isFirstCell
              ? borderRadius
              : '0',
            borderBottomRightRadius: isLastCell
              ? borderRadius
              : '0',
          }),
          borderLeft: isFirstCell
            ? '1px solid #DFE3EA'
            : 'none',
          borderRight: isLastCell
            ? '1px solid #DFE3EA'
            : 'none',
        }}
      />

      {/* 单元格间的垂直分隔线 */}
      {!isLastCell && (
        <div className='absolute top-0 bottom-0 right-0 w-px bg-[#DFE3EA] pointer-events-none' />
      )}
    </>
  )
}
