import React from 'react'

// ==================== 单元格渲染函数 ====================
export const renderTaskNameCell = ({ row, getValue, table }: any) => {
  let serialNumberText = ''
  const parentRow = row.getParentRow()
  
  if (parentRow) {
    const siblingDataRows = parentRow.subRows.filter((d: any) => !d.getIsGrouped())
    const serialIndex = siblingDataRows.findIndex((d: any) => d.id === row.id)
    if (serialIndex !== -1) {
      serialNumberText = `${serialIndex + 1}.`
    }
  } else {
    const topLevelDataRows = table.getPreGroupedRowModel().rows
    const serialIndex = topLevelDataRows.findIndex((d: any) => d.id === row.id)
    if (serialIndex !== -1) {
      serialNumberText = `${serialIndex + 1}.`
    }
  }

  return (
    <div className='flex justify-center items-center h-10'>
      <span className='text-gray-500 text-right w-6 pr-2 flex-shrink-0 text-sm'>
        {serialNumberText}
      </span>
      <span className='flex-grow'>{getValue()}</span>
    </div>
  )
}

export const renderStatusCell = (info: any) => (
  <span className='px-3 py-1 text-sm font-medium text-blue-800 bg-blue-100 rounded-md'>
    {info.getValue()}
  </span>
)

export const renderAssigneeCell = (info: any) => {
  const value = info.getValue()
  if (value && typeof value === 'object' && 'avatar' in value) {
    return (
      <div className='flex items-center gap-2'>
        <img
          src={value.avatar}
          alt={value.name}
          className='w-6 h-6 rounded-full object-cover'
        />
        <span>{value.name}</span>
      </div>
    )
  }
  return value
}

export const renderPriorityCell = (info: any) => {
  const priority = info.getValue()
  let colorClass = 'text-gray-800'
  if (priority === 'P1 最高') colorClass = 'text-red-500 font-bold'
  if (priority === 'P2 高') colorClass = 'text-orange-500 font-bold'
  if (priority === 'P3 中') colorClass = 'text-yellow-600 font-bold'
  if (priority === 'P4 低') colorClass = 'text-gray-500 font-bold'
  return <span className={colorClass}>{priority}</span>
}
