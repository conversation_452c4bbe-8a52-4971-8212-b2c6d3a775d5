// ==================== 纯组件类型定义 ====================
export interface GroupingControlsProps {
  grouping: string[]
  groupableColumns: string[]
  toggleGrouping: (columnId: string) => void
  setGrouping: (grouping: string[]) => void
}

export interface TableContainerProps {
  table: any
  grouping: string[]
  rows: any[]
}

export interface TableRowProps {
  row: any
  rowIndex: number
  rows: any[]
  table: any
  isParentLastInGrandParent: (row: any, index: number) => boolean
  groupEndPositions: Map<string, number>
}

export interface GroupRowProps {
  row: any
  rowIndex: number
  rows: any[]
  table: any
  groupEndPositions: Map<string, number>
}

export interface DataRowProps {
  row: any
  rowIndex: number
  rows: any[]
  table: any
  isParentLastInGrandParent: (row: any, index: number) => boolean
  groupEndPositions: Map<string, number>
}

export interface CellBorderProps {
  row: any
  rowIndex: number
  rows: any[]
  isFirstCell: boolean
  isLastCell: boolean
  isLastRowInGroup: boolean
}

export interface LevelIndicatorsProps {
  row: any
  isFirstCell: boolean
  isLastCell: boolean
}

export interface GroupEndRowsProps {
  row: any
  rowIndex: number
  rows: any[]
  table: any
  groupEndPositions: Map<string, number>
}

export interface DataRowEndSpacingProps {
  row: any
  rowIndex: number
  rows: any[]
  table: any
  isParentLastInGrandParent: (row: any, index: number) => boolean
  groupEndPositions: Map<string, number>
}
