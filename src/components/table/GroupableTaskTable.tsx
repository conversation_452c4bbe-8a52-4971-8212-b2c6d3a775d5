import React from 'react'
import { useTableState, useTableInstance } from './hooks'
import { GroupingControls } from './GroupingControls'
import { TableContainer } from './TableContainer'

// ==================== 主组件 ====================
function GroupableTaskTable() {
  const { data, grouping, setGrouping, columnOrder, setColumnOrder, toggleGrouping } = useTableState()
  const table = useTableInstance(data, grouping, columnOrder, setGrouping, setColumnOrder)
  const { rows } = table.getRowModel()
  const groupableColumns = ['status', 'priority', 'team', 'assignee']

  return (
    <div className='p-4 sm:p-6 lg:p-8 font-sans min-h-screen'>
      <GroupingControls 
        grouping={grouping}
        groupableColumns={groupableColumns}
        toggleGrouping={toggleGrouping}
        setGrouping={setGrouping}
      />
      <TableContainer table={table} grouping={grouping} rows={rows} />
    </div>
  )
}

export default GroupableTaskTable
