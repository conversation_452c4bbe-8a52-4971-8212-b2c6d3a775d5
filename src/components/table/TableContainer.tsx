import React from 'react'
import { TableContainerProps } from './types'
import { TableHeader } from './TableHeader'
import { TableBody } from './TableBody'

// ==================== 表格容器组件 ====================
export const TableContainer: React.FC<TableContainerProps> = ({ table, grouping, rows }) => {
  return (
    <div className='overflow-x-auto'>
      <table
        className='min-w-full text-sm border-separate'
        style={{ borderSpacing: '0' }}
      >
        <TableHeader table={table} grouping={grouping} />
        <TableBody table={table} grouping={grouping} rows={rows} />
      </table>
    </div>
  )
}
