import React from 'react'
import { GroupRowProps } from './types'
import { getBorderRadius } from './utils'
import { GroupEndRows } from './GroupEndRows'

// ==================== 分组行组件（纯组件）====================
export const GroupRow: React.FC<GroupRowProps> = ({
  row,
  rowIndex,
  rows,
  table,
  groupEndPositions,
}) => {
  const borderRadius = getBorderRadius(row.depth)
  const isExpanded = row.getIsExpanded()

  // 判断是否为第一层分组的最后一个分组
  const isLastFirstLevelGroup = (() => {
    if (row.depth !== 0) return false
    const firstLevelGroups = rows.filter(
      (r: any) => r.getIsGrouped() && r.depth === 0
    )
    const currentGroupIndex = firstLevelGroups.findIndex(
      (g: any) => g.id === row.id
    )
    return currentGroupIndex === firstLevelGroups.length - 1
  })()

  // 判断是否为父分组内的最后一个子分组
  const isLastSubGroupInParent = (() => {
    if (row.depth === 0) return false
    for (let i = rowIndex + 1; i < rows.length; i++) {
      const nextRow = rows[i]
      if (nextRow.getIsGrouped()) {
        if (nextRow.depth < row.depth) {
          return true
        }
        if (nextRow.depth === row.depth) {
          return false
        }
      }
    }
    return true
  })()

  return (
    <>
      <tr key={row.id}>
        <td
          colSpan={table.getAllLeafColumns().length}
          className='p-0'
        >
          <div className='flex relative'>
            {/* 渲染父级层级的左边框指示线 */}
            {row.depth > 0 && (
              <div className='absolute left-0 top-0 bottom-0 flex items-stretch'>
                {Array.from({ length: row.depth }).map((_, index) => (
                  <div
                    key={index}
                    className='border-l border-solid border-[#E5E7EB]'
                    style={{
                      marginRight: '23px',
                      height: '100%',
                    }}
                  />
                ))}
              </div>
            )}

            {/* 渲染父级层级的右边框指示线 */}
            {row.depth > 0 && (
              <div className='absolute right-0 top-0 bottom-0 flex items-stretch'>
                {Array.from({ length: row.depth }).map((_, index) => (
                  <div
                    key={index}
                    className='border-r border-solid border-[#E5E7EB]'
                    style={{
                      marginLeft: '24px',
                      height: '100%',
                    }}
                  />
                ))}
              </div>
            )}

            <div
              className={`flex items-center font-medium text-gray-700 w-full border-solid border-[#E5E7EB] py-2 px-3 relative ${
                isExpanded
                  ? 'border-t border-l border-r'
                  : 'border'
              }`}
              style={{
                borderRadius: isExpanded
                  ? `${borderRadius} ${borderRadius} 0 0`
                  : borderRadius,
                marginLeft: `${row.depth * 24}px`,
                marginRight: `${row.depth * 24}px`,
                marginBottom: isExpanded
                  ? '0'
                  : isLastFirstLevelGroup || isLastSubGroupInParent
                  ? '0'
                  : '8px',
              }}
            >
              <button
                onClick={row.getToggleExpandedHandler()}
                className='text-sm p-1 mr-2 hover:bg-gray-200 rounded'
              >
                {isExpanded ? '▼' : '▶'}
              </button>
              <span className='text-xs font-normal bg-white text-gray-600 px-2 py-1 rounded border'>
                L{row.depth + 1}
              </span>
              <span className='ml-2 flex-1'>
                {String(row.groupingValue)}
              </span>
              <span className='text-gray-500 font-normal text-xs bg-white px-2 py-1 rounded border'>
                {row.subRows.length}条
              </span>
            </div>
          </div>
        </td>
      </tr>

      {/* 分组结束的占位行 */}
      <GroupEndRows
        row={row}
        rowIndex={rowIndex}
        rows={rows}
        table={table}
        groupEndPositions={groupEndPositions}
      />
    </>
  )
}
