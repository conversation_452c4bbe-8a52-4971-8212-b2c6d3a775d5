import React from 'react'
import { GroupEndRowsProps } from './types'
import { calculateC } from './utils'

// ==================== 分组结束行组件（纯组件）====================
export const GroupEndRows: React.FC<GroupEndRowsProps> = ({
  row,
  rowIndex,
  rows,
  table,
  groupEndPositions,
}) => {
  // 检查当前分组行是否是某个父级分组的结束位置（不包括自己）
  const groupsToEnd = []
  for (const [groupId, endPosition] of groupEndPositions) {
    if (endPosition === rowIndex && groupId !== row.id) {
      const groupRow = rows.find((r: any) => r.id === groupId)
      if (groupRow && groupRow.depth < row.depth) {
        groupsToEnd.push(groupRow)
      }
    }
  }

  return (
    <>
      {groupsToEnd.map((groupRow: any, gIndex: number) => (
        <tr key={`group-end-${groupRow.id}`}>
          <td
            colSpan={table.getAllLeafColumns().length}
            className={`p-0 ${
              gIndex === groupsToEnd.length - 1 ? 'pb-2' : ''
            } ${
              row.depth == 1 || gIndex > 0
                ? ''
                : 'border-l border-r border-solid border-[#DFE3EA]'
            }`}
          >
            <div className='w-full flex'>
              {Array.from({
                length: calculateC(row.depth, gIndex),
              }).map((_, boxIndex) => (
                <div
                  key={boxIndex}
                  className={`${
                    boxIndex < 1 ? '' : 'border-l'
                  } border-solid border-[#DFE3EA] w-[23px] h-2`}
                />
              ))}
              <div
                className={`flex-1 p-0 border-l border-r border-b border-solid border-[#DFE3EA] ${
                  row.depth < 0 ? 'mx-6' : ''
                }`}
                style={{
                  height: `8px`,
                  borderRadius: `0 0 ${6 + row.depth * 2}px ${
                    6 + row.depth * 2
                  }px`,
                }}
              />
              {Array.from({
                length: calculateC(row.depth, gIndex),
              }).map((_, boxIndex) => (
                <div
                  key={boxIndex}
                  className={`${
                    boxIndex < 1 ? '' : 'border-r'
                  } border-solid border-[#DFE3EA] w-[23px] h-2`}
                />
              ))}
            </div>
          </td>
        </tr>
      ))}
    </>
  )
}
