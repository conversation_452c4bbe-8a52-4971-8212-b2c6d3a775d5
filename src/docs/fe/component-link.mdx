# 组件库调试说明

> 新版组件库与旧版的 tita-ui-pc 调试方式一致

## 1. 在组件项目下执行

```bash
$ yarn link
```

看到如下输出后代表执行成功

```bash
success Registered "@titaui/pc".
info You can now run `yarn link "@titaui/pc"` in the projects where you want to use this package and it will be used instead.
✨  Done in 0.03s.
```

## 2. 在项目目录下执行 

```bash
$ yarn link "@titaui/pc"
```

看到如下输出后代表执行成功

```bash
success Using linked package for "@titaui/pc".
✨  Done in 1.22s.
```

现在就可以启动项目，在组件库内修改后，项目会自动刷新

第二步执行的 `yarn link "@titaui/pc"` 正常只需要执行一次，他的原理其实就是把 node_modules 组件库通过软链链接到组件库项目的目录里，所以当你删除掉项目中的 node_modules，重新安装依赖后还需要重新执行 `yarn link "@titaui/pc"` 来创建软链，否则无法在本地调试。