
let cacheMap = new Map();

export const memoResult = <T extends (...args: any[]) => Promise<any>>(
  key: string,
  fun: T
) => {
  return (...args: Parameters<T>) => {
    return new Promise<ReturnType<T>>((resolve, reject) => {
      const cache = cacheMap.get(key);
      console.info("🎯 Promise cache", cache);
      if (cache) return resolve(cache);
      return fun(...args)
        .then((res) => {
          cacheMap.set(key, res);
          resolve(res);
        })
        .catch(reject);
    });
  };
};

export const isPromise = (val: any): val is Promise<any> => {
  return val && Object.prototype.toString.call(val) === '[object Promise]';
};