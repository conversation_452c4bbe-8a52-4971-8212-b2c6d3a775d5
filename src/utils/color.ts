export function hexToRgb(hex: string) {
  hex = hex.replace('#', '');
  var r = parseInt(hex.substring(0, 2), 16);
  var g = parseInt(hex.substring(2, 4), 16);
  var b = parseInt(hex.substring(4, 6), 16);
  return { r, g, b, a: 1 };
}

export function getRgba(color: string) {
  if (/^#/.test(color)) return hexToRgb(color);
  if (/^rgba/.test(color)) {
    const [_, r, g, b, a] = /rgba\((.*), (.*), (.*), (.*)\)/.exec(color) || [];
    return { r, g, b, a };
  }
  if (/^rgb\(/.test(color)) {
    const [_, r, g, b] = /rgb\((.*), (.*), (.*)\)/.exec(color) || [];
    return { r, g, b, a: 1 };
  }
  return { r: 255, g: 255, b: 255, a: 1 };
}