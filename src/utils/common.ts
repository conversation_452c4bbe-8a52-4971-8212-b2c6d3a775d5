export function openUrl(url?: string) {
  if (typeof url !== 'string') return

  const newTab = window.open(url)
  if (!newTab) return
  newTab.opener = null
  newTab.location = url
}

export const isJsonString = (str: string) => {
  try {
    JSON.parse(str)
  } catch (e) {
    return false
  }
  return str
}

export const findDefaultValue = <T>(arr: T[]) => {
  // @ts-ignore
  return arr.find(item => ![undefined, null].includes(item))
}
