import { IAxiosResponseType } from "@titaui/request/lib/axios";

// @ts-ignore
const Toast = window?.TitaUIPCCmp?.Toast

export const checkResponseError = (res: IAxiosResponseType, showMessage: boolean = true) => {
  if (!res) {
    if (showMessage) console.error('响应数据异常，请稍后再试！');
    return true;
  }
  // @ts-ignore
  if ((res.Code !== undefined && res.Code !== 1) || (res.code !== undefined && res.code !== 1)) {
    // @ts-ignore
    if (showMessage) Toast.Error(res.Message || res.message || '服务器异常，请稍后再试！');
    return true;
  }
  return false;
};

export const toQueryString = (obj: Record<string, any>) => `?${new URLSearchParams(obj).toString()}`