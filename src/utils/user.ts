import { User } from '@tita/model'
import { getUserInfo } from '@tita/utils'

export const getLoginUser = () => {
  const loginUserInfo= getUserInfo()
  return {
    userId: loginUserInfo?.Id,
    name: loginUserInfo?.Name,
    // @ts-ignore
    avatar: {
      hasAvatar: true,
      color: loginUserInfo?.UserAvatar.Color,
      normal: loginUserInfo?.Avatar,
      medium: loginUserInfo?.Avatar,
      small: loginUserInfo?.Avatar,
    },
  } as User
}