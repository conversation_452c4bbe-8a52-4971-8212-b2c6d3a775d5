import dayjs from "dayjs"

export const getDateRange = ({ cycleType, yqmNum, yearNum }: {
  cycleType: number
  yearNum: number
  yqmNum: number
}) => {
  if (cycleType === 1) {
    return [dayjs(`${yearNum}-01-01`), dayjs(`${yearNum}-12-31`)]
  }
  if (cycleType === 2) {
    return {
      1: [dayjs(`${yearNum}-01-01`), dayjs(`${yearNum}-03-31`)],
      2: [dayjs(`${yearNum}-04-01`), dayjs(`${yearNum}-06-30`)],
      3: [dayjs(`${yearNum}-07-01`), dayjs(`${yearNum}-09-30`)],
      4: [dayjs(`${yearNum}-10-01`), dayjs(`${yearNum}-12-31`)]
    }[yqmNum] || []
  }
  if (cycleType === 4) {
    const endDay = dayjs(`${yearNum}-${yqmNum + 1}`).endOf('month')
    return [
      dayjs(`${yearNum}-${yqmNum}-01`),
      dayjs(`${endDay.format('YYYY-MM-DD')}`),
    ]
  }
  if (cycleType === 5) {
    return yqmNum > 1 ? [dayjs(`${yearNum}-07-01`), dayjs(`${yearNum}-12-31`)] : [dayjs(`${yearNum}-01-01`), dayjs(`${yearNum}-6-30`)]
  }
  return []
}