export const numberToChinese = (num: string) => {
  if (!num) return ''

  const chineseNum: Record<string, string> = {
    0: '零',
    1: '一',
    2: '二',
    3: '三',
    4: '四',
    5: '五',
    6: '六',
    7: '七',
    8: '八',
    9: '九',
  };
  return num
    ?.split('')
    .map(item => chineseNum[item] || item)
    .join('');
};

export const handleSearch = (searchValue: string, text: string, styleStr: string) => {
  const regExp = new RegExp(searchValue, 'g')
  text = text.replace(regExp, `<span style="${styleStr}">${searchValue}</span>`)
  return text
}