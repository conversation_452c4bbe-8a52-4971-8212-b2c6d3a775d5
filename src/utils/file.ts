
export const getImageSize = (fileObj: File) => {
  return new Promise<any>((resolve, reject) => {
    // 获取上传的图片的宽高
    var reader = new FileReader();
    reader.readAsDataURL(fileObj);
    reader.onload = function (evt: any) {
        var replaceSrc = evt.target.result;
        var imageObj = new Image();
        imageObj.src = replaceSrc;
        imageObj.onload = function () {
            resolve(imageObj);
        };
        imageObj.onerror = function () {
            reject();
        };
    };
  })
}

export const getFileSizeStr = (fileSize: number) => {
  if (!fileSize) return ''
  if (fileSize < 1024) {
    return `${fileSize}B`;
  } if (fileSize < 1024 * 1024) {
    let temp = fileSize / 1024;
    temp = Number(temp.toFixed(2));
    return `${temp}KB`;
  } if (fileSize < 1024 * 1024 * 1024) {
    let temp = fileSize / (1024 * 1024);
    temp = Number(temp.toFixed(2));
    return `${temp}M`;
  }
  let temp = fileSize / (1024 * 1024 * 1024);
  temp = Number(temp.toFixed(2));
  return `${temp}GB`;
};