// 是否是在钉钉工作台
export const isDingWorkbench = () => {
  const ua = navigator.userAgent.toLowerCase()
  return ua.includes('dingtalk')
}

export const isWechatWorkbench = () => {
  const ua = navigator.userAgent.toLowerCase()
  return ua.match(/wxwork/)
}

export const isFeishuWorkbench = () => {
  const userAgent = navigator.userAgent
  return /Lark|Feishu/i.test(userAgent)
}


let cacheIsMobile: null | boolean = null

export const isMobile = () => {
  if (cacheIsMobile !== null) return cacheIsMobile
  cacheIsMobile = window.matchMedia(
    'only screen and (max-width: 760px)'
  ).matches
  return cacheIsMobile
}
