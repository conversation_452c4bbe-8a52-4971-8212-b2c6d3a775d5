import { rpost } from '@titaui/request'

export interface WeixinAttachment {
  type: number
  url: string
}

export const openWeiPanAttachment = () => {
  return new Promise<WeixinAttachment[]>((resolve, reject) => {
    // @ts-ignore
    window.wx.ready(() => {
      // @ts-ignore
      wx.invoke(
        'wedriveSelectFile',
        {
          selectedFileNum: 10,
        },
        function (res) {
          if (res.err_msg == 'wedriveSelectFile:ok') {
            const selectedFileInfos = res.result.selectedFileInfos
            resolve(selectedFileInfos)
          } else {
            reject(new Error(res.err_msg))
          }
        }
      )
    })
  })
}
