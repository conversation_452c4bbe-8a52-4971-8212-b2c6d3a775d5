import { getBSGlobal, getOpenId } from "@tita/utils";
import flatten from 'lodash/flatten'

export type MentionType =
  | 'user'
  | 'department'
  | 'group'
  | 'all_members'
  | 'all_projects_members'

export enum MentionTypeEnum {
  User = 1,
  AllMembers = 2,
  ProjectOrTarget = 4,
  Department = 15,
  Group = 78,
}
export interface MentionItem {
  mentionId: number
  mentionName: string
  mentionType: MentionTypeEnum
}

export const mentionTypeToEnum = (type: MentionType) => {
  switch (type) {
    case 'user':
      return MentionTypeEnum.User
    case 'all_members':
      return MentionTypeEnum.AllMembers
    case 'all_projects_members':
      return MentionTypeEnum.ProjectOrTarget
    case 'department':
      return MentionTypeEnum.Department
    case 'group':
      return MentionTypeEnum.Group
    default:
      return MentionTypeEnum.User
  }
}

export const textToEditorState = (text: string) => {
  return JSON.stringify({
    doc: {
      type: 'doc',
      content: [
        {
          type: 'paragraph',
          attrs: {
            textAlign: 'left',
          },
          content: [
            {
              type: 'text',
              text: text,
            },
          ],
        },
      ],
    },
    selection: {
      type: 'text',
      anchor: text.length + 1,
      head: text.length + 1,
    },
  })
}

export const appendImgs = (
  editRef: any,
  images: {
    src: string
    alt: string
    width?: number
    height?: number
    resizeAble?: boolean
  }[]
) => {
  editRef.current?.append(
    images.map((image) => ({
      type: 'image',
      attrs: image,
      content: [{ type: 'text', text: image.alt }],
    })),
    {
      insertPos: true,
    }
  )
}

export const appendUsers = (
  editRef: any,
  mentions: {
    name: string
    id: number
    type?: MentionType
  }[]
) =>
  editRef.current.append(
    flatten(
      mentions.map(({ name, id, type = 'user' }) => {
        return [
          {
            type: 'mention',
            attrs: {
              mentionId: id,
              mentionName: name,
              mentionType: type,
            },
          },
          { type: 'text', text: ' ' },
        ]
      })
    ),
    {
      insertPos: true,
    }
  )

  export const getTitaEditorPureText = (content: any, isNewLine?: boolean) => {
    let pureText = '';
  
    if (Array.isArray(content)) {
      content = content.reverse();
    } else {
      content = [content];
    }
    let stack = [...content];
    while (stack.length) {
      const node = stack.pop();
      stack.push(...(node.content || []).reverse());
      if (node.text) pureText += node.text;
      if (node.type == 'mention') {
        const { isTranslate, source, Source } = getBSGlobal('tenantInfo') || {};
        const isWxTranslate = isTranslate && (source === 860 || Source === 860);
        const { mentionType } = node.attrs;
        if (mentionType === 'user') {
          const openId = getOpenId(node.attrs.mentionId, 'userName')?.openId || node.attrs.mentionId;
          pureText += isWxTranslate ? `@$userName=${openId}$ ` : `@${node.attrs.mentionName} `;
        } else if (mentionType === 'department') {
          const openId = getOpenId(node.attrs.mentionId, 'departmentName')?.openId || node.attrs.mentionId;
          pureText += isWxTranslate ? `@$departmentName=${openId}$ ` : `@${node.attrs.mentionName} `;
        }
      }
      if (node.type === 'paragraph' && node.content && isNewLine) {
        pureText += '#tita-n#';
      }
    }
    return pureText.trim();
  }
  