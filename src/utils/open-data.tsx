// @ts-nocheck
import React, { useRef, useLayoutEffect } from 'react'
import { getBSGlobal, getVersion } from '@tita/utils'
import { TenantSource } from '@tita/model'
import OpenDepartmentName from '../stories/business/OpenDepartmentName'
import OpenUserName from '../stories/business/OpenUserName'

const WX_SOURCE = 860
const WX_UA_STR = 'micromessenger'
const FEISHU_SOURCE = 862
const DINGTALK_SOURCE = 861
interface IOpenDataProps {
  id: string | number | undefined // userId || departmentId
  name: string | undefined // userName || departmentName
  corpId?: string
  type?: 'userName' | 'departmentName'
}
const ignorePIds = [-1, 0, 10000]

export const getSource:TenantSource = () => getBSGlobal('tenantInfo')?.Source

export const isWechat = () => getSource() === WX_SOURCE
export const isWxTrial = () => getSource() === WX_SOURCE && getVersion() === 1

const getUA = () => navigator.userAgent.toLowerCase()

export const getOpenId = (id, type) => {
  let openIds
  if (type == 'userName') {
    openIds = window.cacheOpenIdstr;
  } else if (type == 'departmentName') {
    openIds = window.cacheOpenDepartmentIdstr
  }
  return openIds && openIds[id]
}

// const getOpenData = window.openData?.()

// export const OpenUserName = (p: IOpenDataProps) => getOpenData?.({ ...p, type: 'userName' })

// export const OpenDepartmentName = (p) => getOpenData?.({ ...p, type: 'departmentName' })

const openDataMap = {
  user: OpenUserName,
  department: OpenDepartmentName,
}

// name:'wx'|'dd'|'feishu',type:'source'|'env'|'all' = 'all'
export const isPlatform = (name, type = 'all') => {
  switch (name) {
    case 'wx': {
      if (type == 'source') return getSource() === WX_SOURCE
      if (type == 'env') return getUA().includes(WX_UA_STR)
      return getSource() === WX_SOURCE || getUA().includes(WX_UA_STR)
    }
    case 'dd': {
      return getSource() === DINGTALK_SOURCE
    }
    case 'feishu': {
      return getSource() === FEISHU_SOURCE
    }
    default:
      return false
  }
}

export const parseTag = (str) => {
  const source = window.BSGlobal.tenantInfo?.Source
  const ua = navigator.userAgent.toLowerCase()

  const reg = /<\w+[\w\W]*?(userId|departmentId)=["'](\S+?)["'][\w\W]*?>@([\w\W]*?)<\/\w+>/g
  const strReg = /<\w+[\w\s"'\/=\:\.]*?(?:userId|departmentId)=["']\S+?["'][\w\s"'\/=\:\.]*?>[\w\W]*?<\/\w+?>/
  const openDatas = []
  let matchStr = reg.exec(str)

  // if (source != WX_SOURCE && ua.indexOf('micromessenger') == -1)
  //   return <span dangerouslySetInnerHTML={{ '__html': str }} />;

  while (matchStr != null) {
    const OpenDataCompoent = matchStr[1] == 'userId' ? OpenUserName : OpenDepartmentName
    if (!OpenDataCompoent) {
      openDatas.push(
        <span
          dangerouslySetInnerHTML={{ __html: matchStr[0] }}
          onClick={(e) => e.stopPropagation()}
        />,
      )
    } else {
      openDatas.push(
        <span
          style={{ color: '#1890ff', margin: '0 4px' }}
          onClick={(e) => e.stopPropagation()}
        >
          @
          <OpenDataCompoent id={matchStr[2]} name={matchStr[3]} />
        </span>,
      )
    }
    matchStr = reg.exec(str)
  }

  const commonStrs = str?.split(strReg)
  const renderData = []

  for (let i = 0; i < commonStrs.length; i++) {
    renderData.push(
      <span dangerouslySetInnerHTML={{ __html: commonStrs[i] }} />,
    )
    renderData.push(openDatas[i])
  }

  return renderData
}

export const parseString = (str) => {
  // @ts-ignore
  const source = window.BSGlobal.tenantInfo?.Source
  const ua = navigator.userAgent.toLowerCase()
  if (source != WX_SOURCE && ua.indexOf('wxwork') == -1) {
    return <span dangerouslySetInnerHTML={{ __html: str }} />
  }
  const reg = /\[(\w+?)\:(\w+?)\]/g
  const strReg = /\[\w+?\:\w+?\]/
  const openDatas = []
  let matchStr = reg.exec(str)
  if (!matchStr) return parseStringV2(str)
  while (matchStr != null) {
    const OpenDataCompoent = openDataMap[matchStr[2]]
    if (!OpenDataCompoent) {
      const v2Paresed = parseStringV2(matchStr[0])
      openDatas.push(<>{v2Paresed}</>)
    } else {
      openDatas.push(
        <span>
          <OpenDataCompoent
            id={matchStr[1]}
            callFunc="parseString"
            extra={str}
            key={matchStr[1]}
          />
        </span>,
      )
    }
    matchStr = reg.exec(str)
  }
  const commonStrs = str?.split(strReg)
  const renderData = []
  for (let i = 0; i < commonStrs.length; i++) {
    const v2Paresed = parseStringV2(commonStrs[i])
    renderData.push(<>{v2Paresed}</>)
    // renderData.push(
    //   <span dangerouslySetInnerHTML={{ __html: commonStrs[i] }} />,
    // )
    renderData.push(openDatas[i])
  }
  return renderData
}

const openDataMapV2 = {
  userName: OpenUserName,
  departmentName: OpenDepartmentName,
}

export const parseStringV2 = (str) => {
  if (!str) return []
  
  // @ts-ignore
  const source = window.BSGlobal.tenantInfo?.Source
  const ua = navigator.userAgent.toLowerCase()

  if (source != WX_SOURCE && ua.indexOf('micromessenger') == -1) {
    return <span dangerouslySetInnerHTML={{ __html: str }} />
  }

  const reg = /\$(userName|departmentName)\=(.+?)\$/g
  const strReg = /\$(?:userName|departmentName)\=.+?\$/
  const openDatas = []
  let matchStr = reg.exec(str)
  while (matchStr != null) {
    const OpenDataCompoent = openDataMapV2[matchStr[1]]
    if (!OpenDataCompoent) {
      openDatas.push(matchStr[0])
    } else {
      openDatas.push(
        <span>
          <OpenDataCompoent corpId={matchStr[2]} />
        </span>,
      )
    }
    matchStr = reg.exec(str)
  }

  const commonStrs = str?.split(strReg)
  const renderData = []

  for (let i = 0; i < commonStrs.length; i++) {
    renderData.push(
      <span dangerouslySetInnerHTML={{ __html: commonStrs[i] }} />,
    )
    renderData.push(openDatas[i])
  }
  return renderData
}

export const parseOpenDataStringToImg = async (
  str,
  font = {
    lineHeight: 20,
    fontSize: 18,
    fontWeight: '500',
    color: '#3f4755',
  },
) => {
  font = {
    ...{
      lineHeight: 20,
      fontSize: 18,
      fontWeight: '500',
      color: '#3f4755',
    },
    ...(font || {}),
  }
  const reg = /\$(userName|departmentName)\=(.+?)\$/g
  const strReg = /\$(?:userName|departmentName)\=.+?\$/
  const openDatas = []
  let matchStr = reg.exec(str)

  while (matchStr != null) {
    openDatas.push(await getCanvasImg(matchStr[2], matchStr[1], font))
    matchStr = reg.exec(str)
  }

  const commonStrs = str?.split(strReg) || []
  const renderData = []
  for (let i = 0; i < commonStrs.length; i++) {
    renderData.push(
      <span dangerouslySetInnerHTML={{ __html: commonStrs[i] }} />,
    )
    renderData.push(openDatas[i])
  }
  return renderData
}

export const parseHtmlAndTextTag = (str) => {
  const reg = /<\w+[\w\W]*?(userId|departmentId)=["'](\S+?)["'][\w\W]*?>@([\w\W]*?)<\/\w+>/g
  const strReg = /<\w+[\w\s"'\/=\:\.]*?(?:userId|departmentId)=["']\S+?["'][\w\s"'\/=\:\.]*?>[\w\W]*?<\/\w+?>/
  const openDatas = []
  let matchStr = reg.exec(str)

  const renderData = []
  if (matchStr == null) return parseString(str)
  while (matchStr != null) {
    if (matchStr) {
      const OpenDataCompoent = matchStr[1] == 'userId' ? OpenUserName : OpenDepartmentName
      if (!OpenDataCompoent) {
        const strParsed = parseString(matchStr[0])
        openDatas.push(<>{strParsed}</>)
      } else {
        openDatas.push(
          <span
            style={{ color: '#1890ff', margin: '0 4px' }}
            onClick={(e) => e.stopPropagation()}
          >
            @
            <OpenDataCompoent id={matchStr[2]} name={matchStr[3]} />
          </span>,
        )
      }
      matchStr = reg.exec(str)
    }
  }

  const commonStrs = str?.split(strReg)
  for (let i = 0; i < commonStrs.length; i++) {
    const strParsed = parseString(commonStrs[i])
    renderData.push(<>{strParsed}</>)
    renderData.push(openDatas[i])
  }

  return renderData.filter((item) => item !== undefined)
}

export default {
  OpenUserName,
  OpenDepartmentName,
  isWechat,
  isPlatform,
  parseString,
  parseTag,
  parseHtmlAndTextTag,
}
