import cloneDeep from 'lodash/cloneDeep'

export const deepMap = <T extends any, K extends keyof T>(
  arr: T[],
  deepKey: K,
  map: (data: T, parent: T | undefined, indexPath: number[], parentPath: T[]) => any,
  parent: T | undefined = undefined,
  indexPath: number[] = [],
  parentPath: T[] = []
) => {
  const _arr = cloneDeep(arr)
  
  let i = 0
  for (const item of _arr) {
    const currentIndexPath = [...indexPath, i]

    _arr[i] = map(item, parent, currentIndexPath, parentPath)
    
    const childs = _arr[i]?.[deepKey] || []
    if (childs && Array.isArray(childs)) {
      _arr[i][deepKey] = deepMap(
        _arr[i]?.[deepKey] as T[] || [],
        deepKey,
        map,
        _arr[i],
        currentIndexPath,
        [...parentPath, item]
      ) as T[K]
    }
    i++
  }
  return _arr
}
export const move = <T = any>(arr: T[], sourceIndex: number, targetIndex: number) => {
  const _arr = [...arr]
  _arr.splice(targetIndex, 0, _arr.splice(sourceIndex, 1)[0])
  return _arr
}


export const deepFind = <T extends any, K extends keyof T>(
  arr: T[],
  deepKey: K,
  find: (value: T, index: number, obj: T[]) => boolean,
  parent: T | undefined = undefined,
  indexPath: number[] = []
) => {
  const _arr = cloneDeep(arr)
  const path: T[] = []
  let target = undefined

  function deep(item: T, idx: number, arr: T[]) {
    const isTarget = find(item, idx, arr)
    if (isTarget) {
      target = item
      path.push(item)
      return isTarget
    }
    if (item[deepKey]) {
      const childTarget = (item[deepKey] as T[]).find(deep)
      if (childTarget) path.push(item)
    }
    return isTarget
  }
  _arr.forEach(deep)

  if (target) {
    return {
      target,
      path: path.reverse(),
    }
  }
  
  return {
    target: undefined,
    path: undefined,
  }
}
export function arr2Dic<
  T extends Record<string, any>,
  K extends keyof T,
>(
  arr: T[] = [],
  field: K,
): Record<string, T> {
  let dic: Record<string, T> = {}
  arr.forEach((vo) => {
    dic[vo[field]] = vo
  })
  return dic
}

export function arr2Classify<
  T extends Record<string, any>,
  K extends keyof T,
>(
  arr: T[] = [],
  field: K,
): Record<string, T[]> {
  if (!field) return {};

  let dic: Record<string, T[]> = {}
  arr.forEach((vo) => {
    if (!dic[vo[field]]) dic[vo[field]] = []
    dic[vo[field]]?.push(vo)
  })
  return dic
}
