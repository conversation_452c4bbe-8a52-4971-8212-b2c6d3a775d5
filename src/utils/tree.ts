import { Key } from "react"
import filter from 'lodash/filter';
import map from 'lodash/map';
import cloneDeep from "lodash/cloneDeep";

export const deepFind = <T extends Record<string, any>, K extends keyof T>(rootNode: T, idxPath: number[], childField?: K) => {
  const _idxPath = [...idxPath]
  let currentNode = rootNode
  const _childField = childField || 'children'
  while (_idxPath.length) {
    const idx = _idxPath.shift() as number
    currentNode = currentNode[_childField][idx]
  }
  return currentNode
}
export function findByIdxPath<Node, K extends keyof Node>(roots: Node[], idxPath: number[], options?: {
  childrenField?: K
}) {
  const { childrenField = 'children' } = options || {}
  let _roots = roots
  let currentNode: Node | undefined = undefined
  const _idxPath = [...idxPath]
  while (_idxPath.length) {
    const idx: number = _idxPath.shift() as number
    currentNode = _roots[idx]
    // @ts-ignore
    _roots = currentNode[childrenField]
  }
  return currentNode
}

interface TreeNode {
  key: Key
  children?: TreeNode[]
}
export const findNode = (key: Key, treeArray: TreeNode[]): TreeNode | undefined => {
  let result = undefined
  function deep(key: Key, treeArray: TreeNode[]) {
    treeArray.find(node => {
      if (node.key === key) {
        result = node;
        return true;
      }
      if (node.children) return deep(key, node.children)
    })
  }
  deep(key, treeArray)
  return result
}

export const filterParentKeys = (keys: any[], treeArray: TreeNode[]) => {
  let _keys = [...keys]
  let removeKeys: Key[] = []
  _keys.forEach((key, i) => {
    const node = findNode(key, treeArray)
    const otherKeys = _keys.filter(k => k !== key)
    if (!node) return

    const childs = otherKeys.filter(otherKey => !!findNode(otherKey, [node]))
    removeKeys.push(...childs)
  })

  return _keys.filter(key => !removeKeys.includes(key))
}

export function filterTree<T extends TreeNode>(tree: T[], predicate: (node: T) => boolean) {
  return filter(
    map(cloneDeep(tree), node => {
      if (node.children) {
        const children = filterTree(node.children as T[], predicate);
        // @ts-ignore
        node.children = children.length ? children : undefined;
        // @ts-ignore
        node.isLeaf = !children.length;
      }
      return predicate(node) ? node : null;
    }),
    node => node !== null,
  );
}