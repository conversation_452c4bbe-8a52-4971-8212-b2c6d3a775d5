import { rpost } from '@titaui/request'
import dd from 'dingtalk-jsapi'
import uploadAttachment from 'dingtalk-jsapi/api/biz/util/uploadAttachment'
import { sendMessageToSingleChat } from 'dingtalk-jsapi/plugin/coolAppSdk'
import { checkResponseError } from './fetch'

export interface DingAttachment {
  fileId: string
  fileName: string
  fileType: string
  spaceId: string
  fileSize: number
  thumbnail?: Thumbnail
}

export interface Thumbnail {
  mediaId: string
  rotation: number
  width: number
  authMediaId: string
  authCode: string
  height: number
}

export const openDingAttachment = () => {
  return new Promise<DingAttachment[]>((resolve, reject) => {
    dd.ready(function () {
      uploadAttachment({
        types: ['space'],
        compress: true,
        multiple: true,
        max: 9,
        isCopy: 0,
        onSuccess: function (res) {
          resolve(res.data)
        },
        onFail: function (err) {
          reject(err)
        },
      })
    })
  })
}

export const previewDingAttachment = ({
  spaceId,
  fileType,
  fileName,
  fileId,
  fileSize,
}: any) => {
  dd.biz.cspace.preview({
    // @ts-ignore
    corpId: window.BSGlobal?.tenantInfo.corpId,
    spaceId: spaceId,
    fileId: fileId + '',
    fileName,
    fileSize: Number(fileSize),
    fileType,
    onFail: function (err) {
      console.error('预览钉盘文件失败', err)
    },
  })
}

interface IDingCardParams {
  feedId: string
  mentionUsers: any[]
  isShowReplyToUser?: any
  replyUserCommentId?: string
  cardData?: any
}
// 调用钉钉@人发送卡片接口
export const openDingCard: (
  data: IDingCardParams,
  onClose?: ((isClose: boolean) => void) | undefined
) => Promise<void | false> = async (data, onClose) => {
  // @ts-ignore
  const Toast = window?.TitaUIPCCmp?.Toast
  let isDingClose = false
  const {
    feedId,
    isShowReplyToUser,
    replyUserCommentId,
    cardData: propsCardData,
    mentionUsers,
  } = data
  const sentData: any = {}
  // 钉钉环境如果开启了同步@人卡片的入口，同时打开了开关需要先弹出@人卡片
  // 调用后端接口获取调用钉钉弹窗所需要的信息
  sentData.FeedId = feedId
  sentData.CommentId = 0
  sentData.ObjId = 0
  sentData.ObjType = 0
  sentData.Content = sentData?.Content || ' '
  sentData.mentionUsers = mentionUsers
  sentData.version = '1'
  if (isShowReplyToUser) {
    sentData.CommentId = replyUserCommentId
  }
  const res = await rpost<any>('v2')('/dingCard/get', sentData)
  if (checkResponseError(res)) return false

  // 调起钉钉弹窗，用户选择取消之后就直接return
  const { userIdList, cardData, clientId, corpId, cardTemplateId, outTrackId } =
    res.Data
  if (!cardData?.objName) {
    cardData.objType = propsCardData?.objType
    cardData.objName = propsCardData?.objName
  }
  const dingRes = await sendMessageToSingleChat({
    context: {
      clientId,
      corpId, // 根据对应场景获取 corpId
    },
    userIdList,
    sendCardRequest: {
      cardData: {
        cardParamMap: {
          ...cardData,
        },
      },
      cardTemplateId,
      outTrackId,
    },
  })
  if (dingRes.errorCode === 22 || dingRes.errorCode === '22') {
    isDingClose = true
    onClose?.(true)
    return
  }
  // 钉钉版本过低
  if (dingRes.errorCode === 7 || dingRes.errorCode === '7') {
    Toast?.Error('当前钉钉版本较低，不支持该API，需要升级至最新版本的钉钉')
    return
  }
  // @人卡片增加埋点
  // @ts-ignore
  window.titaTracker?.('action').record({
    productName: '计划表',
    actionGroup: '任务沟通',
    actionName: '发送到钉钉',
  })

  if (isDingClose) return false
}
