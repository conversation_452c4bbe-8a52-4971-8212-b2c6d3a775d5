import axios, { AxiosRequestConfig } from 'axios'
import { getBSGlobal, getTenantInfo, getUserInfo, isTestEnv } from '@tita/utils'

const instance = axios.create()

instance.interceptors.response.use((res) => {
  if (res.status !== 200) return Promise.reject(res)
  if (res.data.Code != 1 && res.data.code != 1) {
    // if(res.config.method == 'post' && res.data.Message){
    //   Toast.Error(res.data.Message)
    // }
    if (res.data.Code === 401 || res.data.code === 401) history.back()
    return Promise.reject(res.data.Message || res.data.message)
  }
  return Promise.resolve(res.data.Data || res.data.data || res.data)
})

interface NetworkOptions extends AxiosRequestConfig {
  version?: 'v1' | 'v2' | 'v3'
  url: string
  method: string
}

export default function network<T>({
  version = 'v1',
  url,
  ...options
}: NetworkOptions): Promise<T> {
  const uid = getUserInfo().Id
  const tid = getTenantInfo().Id
  const apiServer = getBSGlobal('apiPath')
  options['url'] = apiServer + `/api/${version}/${tid}/${uid}/${url}`
  return instance.request<T>(options) as any
}

export function getCloud<T>({ url, appId = 'Crm', ...options }) {
  const tenantId = getTenantInfo().Id
  let apiServer = `//cloud.tita.${
    isTestEnv()
      ? 'work'
      : location?.origin?.includes('tita.net')
      ? 'net'
      : 'com'
  }`
  if (location.host.includes('localhost')) apiServer = `//${location.host}`
  options.url = `${apiServer}${url}`
  options.withCredentials = true

  if (options.query?.method === 'filter') {
    options.query = options.query.data
  } else if (
    !(
      options.url.includes('/paas/api/data/create') ||
      options.url.includes('/paas/api/data/update') ||
      options.url.includes('/crm/pool/receivePool')
    )
  ) {
    options.query = {
      ...options.query,
      app: appId,
      tenantId,
    }
  }
  const method = options?.method?.toUpperCase() || 'GET'
  if (method === 'GET') {
    options.params = options.query
  } else {
    options.data = options.query
  }
  delete options.query
  return instance.request(options) as Promise<T>
}
