import { rpost } from '@titaui/request'

export interface FeishuAttachment {
  list: Array<object>
}
export interface FeishuDocsAttachment {
  fileList: Array<object>
}

export const openFeishuFileAttachment = () => {
  return new Promise<FeishuAttachment[]>((resolve, reject) => {
    const filePickerOptions = {
      maxNum: 10,
      pickerTitle: '请选择文件',
      pickerConfirm: '确定',
      isSystem: false,
      success(res: { errMsg: string; list: Array<object> }) {
        if (res.errMsg === 'filePicker:ok') {
          resolve(res.list)
        } else {
          reject(new Error(res.errMsg))
        }
      },
      fail(res: { errMsg: string }) {
        reject(new Error(`filePicker fail: ${res.errMsg}`))
      },
    }
    // @ts-ignore
    window.tt?.filePicker(filePickerOptions)
  })
}
export const openFeishuDocumentAttachment = () => {
  return new Promise<FeishuDocsAttachment[]>((resolve, reject) => {
    const docsPickerOptions = {
      maxNum: 10,
      pickerTitle: '请选择文档',
      pickerConfirm: '确定',
      success(res: { errMsg: string; list: Array<object> }) {
        if (res.errMsg === 'docsPicker:ok') {
          resolve(res.fileList)
        } else {
          reject(new Error(res.errMsg))
        }
      },
      fail(res: { errMsg: string }) {
        reject(new Error(`docsPicker fail: ${res.errMsg}`))
      },
    }
    // @ts-ignore
    window.tt?.docsPicker(docsPickerOptions)
  })
}
