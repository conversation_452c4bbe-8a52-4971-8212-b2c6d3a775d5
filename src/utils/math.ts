import Decimal from 'decimal.js'

export function splitInteger(n1: number, m: number): number[] {
  if (n1 <= 0) return new Array(m).fill(0)
  const n = Math.trunc(n1)
  // 小数部分
  const decimal = new Decimal(n1).minus(n)

  // 如果评分数量大于总数,比如需要将 2 均分成 3 份,那最终的结果就是 [1,1,0]
  if (m > n) {
    const result = new Array(n).fill(1)
    result[result.length - 1] = new Decimal(result[result.length - 1]).plus(decimal).toNumber()
    result.push(...new Array(m - n).fill(0))
    return result
  }

  // 计算每个元素的初始值
  const initial = Math.floor(n / m) || 1;
  const result = new Array(m).fill(initial)
  result[result.length - 1] += new Decimal(n - initial * m || 0).plus(decimal).toNumber()
  return result
}