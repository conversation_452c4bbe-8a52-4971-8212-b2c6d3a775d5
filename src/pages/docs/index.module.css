.docs {
  /* background-color: #001529; */
}
.header {
  backdrop-filter: blur(8px);
}
.docs-nav {
  padding: 0 20px;
  position: sticky;
  top: 70px;
  transition: transform 0.3s, opacity 0.3s;
}
.docs-nav.docs-nav--hide {
  transform: translateX(20px);
  opacity: 0;
}
.docs-nav li + li {
  color: #999;
  margin-top: 5px;
}
.docs-nav a {
  color: inherit;
}
.docContent {
  width: 100%;
  max-width: calc(100vw - 13rem);
  height: max-content;
}
.docContentMdx {
  width: calc(100vw - 13rem - 18rem);
}
.footer {
  bottom: 0;
  width: 100%;
  background: #001529;
  color: rgba(255, 255, 255, 0.65);
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  left: 0;
  flex-shrink: 0;
}
.footer a {
  color: inherit;
  font-size: 12px;
}
.footer a:hover {
  color: #fff;
}
