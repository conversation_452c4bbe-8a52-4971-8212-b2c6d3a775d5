import { Outlet, useMatches, useNavigate } from 'react-router-dom'
import classNames from 'classnames'
import { arr2DicDeep, deepMap, findFirstLeaf } from '@/libs/array'
import { ThemeToggle, useTheme } from '@/components/ThemeToggle'
import docsRoutes from '@/docs.routes'
import styles from './index.module.css'
import { useCallback, useLayoutEffect, useMemo, useRef, useState } from 'react'
import { DocsRouteObject } from '@/utils/route'
import { Link } from 'react-router-dom'
import { useEffect } from 'react'
import flatten from 'lodash/flatten'
import '@/stories/index.scss'
import { ITitaScrollRef, MenuItem, Menus, MenusItem, Search, TitaScroll, Page } from '@tita/ui'
import "@tita/ui/dist/index.css"

const docsRoutesDic = arr2DicDeep(docsRoutes, 'path', 'children') as Record<
  string,
  DocsRouteObject
>

type Nav = {
  title: any
  id: string
  level: number
}

export default function Expenses() {
  const navigate = useNavigate()
  const matches = useMatches()
  const [theme] = useTheme()

  const scrollRef = useRef<ITitaScrollRef>()

  // 自动跳转到对应的标题位置
  useEffect(() => {
    if (location.hash) {
      setTimeout(() => {
        scrollRef.current?.scrollToChild(decodeURIComponent(location.hash))
      }, 500)
    }
  }, [])
  
  const pathname = matches[matches.length - 1].pathname
  const modulePath = matches[1].pathname
  
  const menuData = docsRoutes.find(({ path }) => path === modulePath) || { children: [] }
  // @ts-ignore
  const openKeys = menuData.children.map(({ key }) => key)

  const currentComponents = flatten(menuData.children.map(item => item.children))
  const onSearchHandler = useCallback<(query: string) => Promise<MenuItem[]>>((query) => {
    // @ts-ignore
    return Promise.resolve(currentComponents.filter(item => item?.label.includes(query)).map(({ label, key }) => ({ label, value: key })))
  }, [currentComponents])

  useEffect(() => {
    // 跳转至模块第一个页面
    if (matches.length === 2) {
      const firstDoc = findFirstLeaf(menuData.children, 'children')
      
      if (firstDoc && firstDoc.path) navigate(firstDoc.path)
    }
  }, [matches])

  const [nav, setNav] = useState<Nav[]>([])
  const docContentRef = useRef<any>()

  useLayoutEffect(() => {
    if (docContentRef.current) {
      setNav([])
      setTimeout(() => {
        const hList = docContentRef.current.querySelectorAll(
          '.mdx-h'
        ) as NodeListOf<Element & { innerText: string }>

        const navs: Nav[] = Array.from(hList).map(
          ({ innerText, id, nodeName }) => ({
            title: innerText,
            id,
            level: +nodeName.slice(1) - 1,
          })
        )
        setNav(navs)
      }, 1000)
    }
  }, [docContentRef.current, pathname])

  const onSelectChange = useCallback((key: string) => {
    navigate(key)
  }, [])

  const menuOptions = useMemo(() => {
    return menuData.children.flatMap(item => {
      return [
        // @ts-ignore
        { label: item.label, key: item.key, isGroup: true },
        // @ts-ignore
        ...item.children
      ]
    })
  }, [menuData.children])

  return (
    <Page className=' dark:bg-gradient-to-tr'>
      <header
        className={classNames(
          styles.header,
          'fixed top-0 w-screen h-16 flex justify-between items-center px-7 bg-slate-100/75 dark:bg-slate-900/75 z-10'
        )}
      >
        <div className='font-bold text-2xl dark:text-slate-200 text-slate-700'>
          TITA-FE
        </div>

        <div className='flex items-center space-x-10'>
          <ul className='flex space-x-6'>
            {docsRoutes.map(({ path, label }) => (
              <li key={path}><Link className={classNames('dark:text-slate-300 dark:hover:text-slate-100 text-slate-800', {
                'dark:text-blue-500': path === modulePath
              })} to={path as string}>{label}</Link></li>
            ))}
          </ul>
          <ThemeToggle panelClassName='mt-8' />
        </div>
      </header>

      <main
        className={classNames(
          styles.docs,
          'flex h-screen'
        )}
      >
        <div className='w-52 mt-16 flex flex-col overflow-hidden'>
          <div className='p-10px space-y-12px'>
            <Search onSearch={onSearchHandler} placeholder='搜索组件' onSelect={key => navigate(key as string)} />
            <Menus height="80vh" items={menuOptions} selectedKey={pathname} onChange={onSelectChange} shadowTheme="white" />
          </div>
        </div>
        <TitaScroll id="mainScroll" ref={scrollRef} className="flex-1 overflow-y-auto flex flex-col items-center relative dark:bg-slate-800" colBarInset>
          <div
            ref={docContentRef}
            className={classNames(styles.docContent, 'py-10 px-16 flex pt-16')}
          >
            <div className={classNames(styles.docContentMdx, 'w-full overflow-hidden')}>
              <Outlet />
            </div>
            <div className={classNames('w-40 shrink-0')}>
              <nav
                className={classNames(styles['docs-nav'], {
                  [styles['docs-nav--hide']]: !nav.length,
                })}
              >
                <ul>
                  {nav.map(({ level, title, id }) => (
                    <li
                      key={id}
                      className='hover:text-blue-400'
                      style={{
                        paddingLeft: (level - 1) * 15,
                      }}
                    >
                      <a href={`#${id}`}>{title}</a>
                    </li>
                  ))}
                </ul>
              </nav>
            </div>
          </div>
        </TitaScroll>
      </main>
    </Page>
  )
}
