<!DOCTYPE html>
<html lang="en" class='dark [--scroll-mt:9.875rem] lg:[--scroll-mt:6.3125rem]'>
  <head>
    <meta charset="UTF-8" />
    <meta name='theme-color' content='#f8fafc' />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Vite + React + TS</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>

    <script>
      try {
        if (localStorage.theme === 'dark' || (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
          document.documentElement.classList.add('dark')
          document.querySelector('meta[name="theme-color"]').setAttribute('content', '#0B1120')
        } else {
          document.documentElement.classList.remove('dark')
        }
      } catch (_) {}
    </script>
  </body>
</html>
