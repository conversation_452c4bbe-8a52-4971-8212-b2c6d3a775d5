import { defineConfig } from 'tsup'
import { sassPlugin } from 'esbuild-sass-plugin'

export default defineConfig((options) => {
  return {
    entry: [
      './src/stories/index.tsx',
      // './src/stories/*.tsx',
      // './src/stories/**/index.tsx',
      // '!./src/stories/*.stories.tsx',
    ],
    minify: !options.watch,
    treeshake: true,
    // dts: true,
    format: ['esm'],
    clean: false,
    esbuildPlugins: [sassPlugin()]
  }
})
