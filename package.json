{"name": "@tita/table", "version": "0.0.2-beta.1", "type": "module", "publishConfig": {"registry": "http://***********:4873/"}, "sideEffects": ["*.css", "*.less", "*.scss", "*.svg", "*.png", "*.jpg", "*.jpeg", "*.gif", "*.mp4", "*.eot", "*.ttf", "*.woff", "*.woff2", "*.txt", "*.json"], "files": ["dist", "esm", "lib"], "main": "esm/stories/index.js", "types": "esm/stories/index.d.ts", "scripts": {"dev": "vite --port 82", "build:docs": "vite build", "preview": "vite preview --port 3300", "build": "vite build", "build:lib": "npm run clean && npx father build && tsup --target es5 --sourcemap", "dev:lib": "npx father dev --incremental && tsup --target es5 --watch --sourcemap", "serve": "serve ./dist -p 3300", "start": "pm2 start ./ecosystem.config.cjs", "storybook": "start-storybook -p 6006", "build-storybook": "build-storybook", "publish:beta": "npm run version:beta && npm publish --tag beta", "version:patch": "npm version patch", "version:beta": "npm version prerelease --preid=beta -m 'CI: Automatically update version to %s.'", "prepublishOnly": "npm run build:lib", "postversion": "git push && git push --tags", "clean": "rm -rf ./dist ./esm ./lib"}, "dependencies": {"@ahooksjs/use-url-state": "^3.5.1", "@ant-design/charts": "^1.4.2", "@ant-design/icons": "^4.7.0", "@ant-design/plots": "^1.2.5", "@antv/g2": "5.0.0-rc.1", "@code-hike/highlighter": "^0.3.0", "@code-hike/mdx": "^0.7.4", "@codemirror/commands": "^6.8.1", "@codemirror/lang-javascript": "^6.2.3", "@codemirror/lang-json": "^6.0.1", "@codemirror/language": "^6.11.0", "@codemirror/lint": "^6.8.5", "@codemirror/state": "^6.5.2", "@codemirror/view": "^6.36.5", "@dagrejs/dagre": "^1.0.4", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/core": "^11.0.0", "@emotion/react": "^11.10.6", "@formulajs/formulajs": "^4.4.11", "@mdx-js/react": "^2.1.5", "@mdx-js/rollup": "^2.1.5", "@react-spring/web": "^9.6.0", "@reduxjs/toolkit": "^1.9.0", "@tanstack/react-table": "^8.21.3", "@tanstack/react-virtual": "^3.13.12", "@tita/hooks": "^0.0.25", "@tita/model": "0.0.31", "@tita/ui": "0.0.306-beta.16", "@tita/utils": "^3.0.17", "@titaui/axios-auth-request": "2.0.12", "@titaui/rc-pagination2": "^1.0.9", "@titaui/rc-tree2": "4.1.25", "@titaui/rc-trigger": "5.2.5-beta.3", "@titaui/request": "^1.1.14", "@types/jquery": "^3.5.16", "@uiw/codemirror-extensions-basic-setup": "^4.23.10", "@uiw/react-codemirror": "4.23.10", "@use-gesture/react": "^10.2.23", "ace-builds": "1.4.13", "ahooks": "^3.7.2", "antd": "^4.24.1", "axios": "^1.7.2", "can-use-dom": "^0.1.0", "classnames": "^2.3.2", "codemirror": "^6.65.7", "consola": "^2.15.3", "dayjs": "^1.11.7", "decimal.js": "^10.4.3", "dingtalk-jsapi": "3.1.1", "dnd-core": "^16.0.1", "echarts": "^5.4.1", "echarts-liquidfill": "^3.1.0", "eslint-linter-browserify": "^9.24.0", "esprima": "^4.0.1", "excel-formula-parser": "^1.1.0", "framer-motion": "^10.15.0", "globals": "^16.0.0", "html-react-parser": "^5.2.5", "immer": "^10.0.4", "jquery": "^3.6.1", "less": "^4.1.3", "lodash": "^4.17.21", "moment": "^2.30.1", "rc-dialog": "^9.4.0", "rc-drawer": "4.3.1", "rc-notification": "^3.0.1", "rc-progress": "^3.4.1", "rc-select": "^14.16.4", "rc-slider": "^10.6.2", "rc-switch": "^4.0.0", "rc-textarea": "^1.0.1", "rc-tooltip": "^5.3.0", "rc-tree": "^5.7.4", "rc-trigger": "^5.3.4", "rc-upload": "4.8.1", "react": "^18.2.0", "react-accessible-treeview": "^2.5.6", "react-ace": "^9.4.0", "react-arborist": "^3.1.0", "react-beautiful-dnd": "^13.1.1", "react-color": "2.19.3", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-grid-layout": "^1.5.2", "react-icon-cloud": "^4.1.4", "react-redux": "^8.0.5", "react-router-dom": "^6.4.2", "react-simple-mind": "^0.0.3", "react-transition-group": "^4.4.5", "react-virtualized": "^9.22.5", "react-window": "^1.8.9", "reactflow": "^11.9.4", "remark-rehype": "8.1.0", "resize-observer-polyfill": "^1.5.1", "rsuite": "^5.35.0", "sass": "^1.56.0", "shiki": "^0.11.1", "showdown": "^2.1.0", "styled-components": "^5.3.9", "uuid": "^3.1.0", "viewerjs": "^1.11.5", "zustand": "^4.1.4", "zz-mind-react": "^0.2.1"}, "devDependencies": {"@babel/core": "^7.19.6", "@faker-js/faker": "^9.9.0", "@storybook/addon-actions": "^6.5.13", "@storybook/addon-backgrounds": "^6.5.13", "@storybook/addon-docs": "^6.5.13", "@storybook/addon-essentials": "^6.5.13", "@storybook/addon-interactions": "^6.5.13", "@storybook/addon-links": "^6.5.13", "@storybook/addon-measure": "^6.5.13", "@storybook/addon-outline": "^6.5.13", "@storybook/addons": "^6.5.13", "@storybook/builder-vite": "^0.2.4", "@storybook/channel-postmessage": "^6.5.13", "@storybook/channel-websocket": "^6.5.13", "@storybook/client-api": "^6.5.13", "@storybook/preview-web": "^6.5.13", "@storybook/react": "^6.5.13", "@storybook/testing-library": "0.0.13", "@swc/core": "^1.3.40", "@types/esprima": "^4.0.5", "@types/lodash": "^4.14.191", "@types/mockjs": "^1.0.10", "@types/node": "^20.12.5", "@types/react": "^18.0.6", "@types/react-beautiful-dnd": "^13.1.3", "@types/react-color": "^3.0.6", "@types/react-dom": "^18.0.6", "@types/react-router-dom": "^5.3.3", "@types/react-transition-group": "^4.4.5", "@types/react-virtualized": "^9.21.29", "@types/react-window": "^1.8.5", "@types/styled-components": "^5.1.26", "@vitejs/plugin-react": "^2.1.0", "autoprefixer": "^10.4.12", "babel-loader": "^8.2.5", "esbuild-sass-plugin": "^2.4.0", "father": "^4.5.2", "mockjs": "^1.1.0", "postcss": "^8.4.18", "remark-code-import": "^1.1.1", "strip-indent": "^4.0.0", "tailwindcss": "^3.2.1", "tsup": "^6.3.0", "typescript": "^4.6.4", "unist-util-visit": "^4.1.1", "vite": "^3.1.0", "vite-plugin-style-import": "^2.0.0"}, "peerDependencies": {"@emotion/core": "^11.0.0", "@emotion/react": "^11.10.6", "react": "^18.2.0", "react-dom": "^18.2.0", "rsuite": "^5.35.0", "styled-components": "^5.3.9"}, "resolutions": {"react": "18.2.0", "react-dom": "18.2.0", "@dagrejs/graphlib": "2.1.4"}}