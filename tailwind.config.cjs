/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: 'class',
  content: ['./src/**/*.{html,tsx}'],
  theme: {
    extend: {
      colors: {
        'primary': '#2879FF',
        'page': '#f0f4fa',
        'page-dark': '#1f2224',
        'panel': '#ffffff',
        'panel-dark': '#181a1b',
        '#141c28': '#141c28',
        '#3f4755': '#3f4755',
        '#6f7886': '#6f7886',
        '#89919f': '#89919f',
        '#a4acb9': '#a4acb9',
        '#bfc7d5': '#bfc7d5',
        '#ff7e3d': '#FF7E3D',
      },
      spacing: {
        '2px': '2px',
        '4px': '4px',
        '6px': '6px',
        '8px': '8px',
        '10px': '10px',
        '12px': '12px',
        '14px': '14px',
        '16px': '16px',
        '20px': '20px',
        '24px': '24px',
        '28px': '28px',
        '32px': '32px',
        '34px': '34px',
      }
    },
  },
  plugins: [],
}
