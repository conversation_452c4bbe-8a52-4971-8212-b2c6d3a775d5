import React, { FC } from 'react'
import classNames from 'classnames'
import './index.scss'

export interface I$T{F|componentName}Props {
  children?: React.ReactNode
  className?: string
  style?: React.CSSProperties
}

const preCls = 'tita-ui-$T{-|componentName}'

export const $T{F|componentName}: FC<I$T{F|componentName}Props> = React.memo(({ className, style }) => {
  return (
    <div className={classNames(preCls, className)} style={style}>
      $T{F|componentName}
    </div>
  )
})

export default $T{F|componentName}
