[{"name": "component:base", "params": ["componentName", "componentLabel", ["componentType", ["base", "data", "corner", "utils", "chart", "dataInput", "navigation", "layout", "feedback"]]], "root": "./src", "templates": [{"name": "$T{F|componentName}", "type": "dir", "root": "stories/base", "files": [{"name": "index.tsx", "template": "component-base/index.tsx.tpl"}, {"name": "index.scss", "template": "component-base/index.scss.tpl"}]}, {"name": "examples", "type": "dir", "root": "stories/base/$T{F|componentName}", "files": [{"name": "base.tsx", "template": "component-base/example.tsx.tpl"}]}, {"name": "$T{f|componentName}.mdx", "root": "docs/components/base", "template": "component-base/index.mdx.tpl"}, {"type": "replace", "replaceFile": "docs.routes.ts", "replaceOptions": [{"target": "componentType:base:$T{componentType}", "template": "component-base/route.tpl"}]}, {"type": "replace", "replaceFile": "stories/index.tsx", "replaceOptions": [{"target": "exportComponent:base", "template": "component-base/export.tpl"}]}]}, {"name": "component:business", "params": ["componentName", "componentLabel", ["componentType", ["base"]]], "root": "./src", "templates": [{"name": "$T{F|componentName}", "type": "dir", "root": "stories/business", "files": [{"name": "index.tsx", "template": "component-business/index.tsx.tpl"}, {"name": "index.scss", "template": "component-business/index.scss.tpl"}]}, {"name": "examples", "type": "dir", "root": "stories/business/$T{F|componentName}", "files": [{"name": "base.tsx", "template": "component-business/example.tsx.tpl"}]}, {"name": "$T{f|componentName}.mdx", "root": "docs/components/business", "template": "component-business/index.mdx.tpl"}, {"type": "replace", "replaceFile": "docs.routes.ts", "replaceOptions": [{"target": "componentType:business:$T{componentType}", "template": "component-business/route.tpl"}]}, {"type": "replace", "replaceFile": "stories/index.tsx", "replaceOptions": [{"target": "exportComponent:business", "template": "component-business/export.tpl"}]}]}]